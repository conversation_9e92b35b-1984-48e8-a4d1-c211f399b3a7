.file-path-select{
    &-container{
        display: flex;
        position: relative;

        &.acud-form-col-1{
            grid-column-gap: 0;
        }
    }

    &-select{
        visibility: hidden;
        position: absolute;
        width: 100%;

        &-item{
            display: flex;
            justify-content: space-between;
            align-items: center;

            &-value{
                display: flex;
                align-items: center;
                color: rgb(51 51 51);
            }

            &-path{
                color: #84868C;
                margin-left: 12px;
            }
        }
    }

    &-form{
        width: 100%;

        .acud-form-item-control{
            max-width: none !important;
        }
    }
    // 需要低于新建 modal
    &-dropdown{
        z-index: 1000;
    }

    &-breadcrumb{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 18px 12px 14px;
        height: 20px;

        .acud-breadcrumb{
             flex-wrap: wrap;

           a{
                font-size: 12px;
            }
        }

         .acud-btn{
            padding: 0;
        }
    }

    &-blank-icon{
        height: 191px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: black;
        flex-direction: column;
        width: 100%;

        .icon{
            width: 80px;
            height: 80px;
            background-size: 80px 80px;
            margin-bottom: 12px
        }

        &-search{
            .icon();

            background-image: url("../../assets/png/integration/blank-search.png");
        }

        &-list{
            .icon();

            background-image: url("../../assets/png/integration/blank-list.png");
        }
    }
}
import React, {useState, useContext, useEffect, useRef} from 'react';
import {Dropdown, Modal, Button, Tooltip, Loading} from 'acud';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import type {ComputeResourceItem} from '@api/Compute';
import {getComputeResourceList} from '@api/Compute';
import {useRequest, useMemoizedFn} from 'ahooks';
import {WorkspaceContext} from '@pages/index';
import {connectToCompute, disconnectFromCompute, getComputeSessionStatus} from '@api/WorkArea';
import {Poll} from '@lumino/polling';
import {useNotebook} from '@store/notebookStatehooks';
import useUrlState from '@ahooksjs/use-url-state';
import {STATUS} from '@pages/Compute/config';
import Arrow from '@assets/originSvg/notebook/arrow.svg';
import {useNotebookStore} from '@baidu/db-jupyter-react/lib/components/notebook';
import useEnv from '@hooks/useEnv';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {TooltipType} from '@components/AuthComponents/constants';
import urls from '@utils/urls';

const cx = classNames.bind(styles);

const ComputeSelector: React.FC = () => {
  const {isPrivate} = useEnv();
  const authList = useWorkspaceAuth([Privilege.ComputeMenu]);
  const hasComputeMenu = authList[Privilege.ComputeMenu];

  const [urlState, setUrlState] = useUrlState();
  const {workspaceId} = useContext(WorkspaceContext);
  const {
    activeCompute,
    connectStatus,
    sessionId,
    computeId,
    setNotebookActiveCompute,
    updateNotebookComputeStatus,
    updateNotebookSessionId,
    updateNotebookComputeId,
    updateNotebookKernelId,
    resetNotebookStates
  } = useNotebook();

  const notebookStore = useNotebookStore();

  // 常驻实例
  const [computeList, setComputeList] = useState<ComputeResourceItem[]>([]);
  // 查询检索实例
  const [searchComputeList, setSearchComputeList] = useState<ComputeResourceItem[]>([]);

  const {runAsync: getComputeList, loading: getComputeListLoading} = useRequest(getComputeResourceList, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        const RaySparkList = res.result.computes.filter((item) => ['Ray', 'Spark'].includes(item.engine));
        const DorisList = res.result.computes.filter((item) => item.engine === 'Doris');
        // 排序逻辑：status为RUNNING的放在前面
        const sortedRayList = [...RaySparkList].sort((a, b) => {
          if (a.status === 'RUNNING' && b.status !== 'RUNNING') return -1;
          if (a.status !== 'RUNNING' && b.status === 'RUNNING') return 1;
          return 0;
        });
        const sortedDorisList = [...DorisList].sort((a, b) => {
          if (a.status === 'RUNNING' && b.status !== 'RUNNING') return -1;
          if (a.status !== 'RUNNING' && b.status === 'RUNNING') return 1;
          return 0;
        });
        setComputeList(sortedRayList);
        setSearchComputeList(sortedDorisList);
      }
    }
  });

  const handleVisibleChange = (visible: boolean) => {
    if (visible) {
      getComputeList({workspaceId});
    }
  };

  // 更新 notebook store 的连接状态，用于在cellSideBar中使用
  useEffect(() => {
    notebookStore.setConnectStatus({
      id: urlState.notebookId,
      connectStatus
    });
  }, [connectStatus]);

  // 更新 notebook store 的计算资源，用于在cellSideBar中使用
  useEffect(() => {
    notebookStore.setCustomData({
      id: urlState.notebookId,
      customData: {
        compute: activeCompute
      }
    });
  }, [activeCompute]);

  // 断开连接
  const pollRef = useRef<Poll | null>(null);
  const {runAsync: disconnectFromComputeRun} = useRequest(disconnectFromCompute, {
    manual: true
  });
  const disconnectFromComputeHandler = useMemoizedFn((poll: Poll) => {
    clearConnection(poll);
    if (!sessionId || !computeId) {
      abortControllerRef.current?.abort();
      return;
    }
    disconnectFromComputeRun({
      workspaceId,
      sessionId,
      computeId
    });
  });
  useEffect(() => {
    return () => {
      disconnectFromComputeHandler(pollRef.current!);
    };
  }, [disconnectFromComputeHandler]);

  // 轮询session状态
  function startSessionPolling(sessionId: string, computeId: string) {
    async function sessionFactory(sessionId: string, computeId: string, poll: Poll) {
      try {
        const res = await getComputeSessionStatus({workspaceId, sessionId, computeId});
        if (!res.success) {
          throw new Error('Failed to get compute session');
        }
        const {status} = res.result;

        updateNotebookComputeStatus(status);

        if (['SESSION_ERROR', 'SERVER_ERROR'].includes(status)) {
          poll.dispose();
          resetNotebookStates();
        }
      } catch (error) {
        console.error(error);
      }
    }
    if (pollRef.current) {
      pollRef.current.dispose();
    }
    pollRef.current = new Poll({
      auto: false,
      factory: () => sessionFactory(sessionId, computeId, pollRef.current!),
      frequency: {
        interval: 5 * 1000,
        backoff: true,
        max: 300 * 1000
      },
      name: `db-session-polling-${sessionId}`
    });
    pollRef.current!.start();
  }

  // 资源连接
  const abortControllerRef = useRef<AbortController>(new AbortController());
  useEffect(() => {
    return () => {
      abortControllerRef.current.abort();
    };
  }, []);
  const {runAsync: connectToComputeRun} = useRequest(connectToCompute, {
    manual: true
  });

  async function connectToComputeHandler(compute: ComputeResourceItem) {
    // 在每次调用前重新创建AbortController
    abortControllerRef.current.abort(); // 中止之前可能存在的请求
    abortControllerRef.current = new AbortController(); // 创建新的AbortController

    setNotebookActiveCompute(compute);
    updateNotebookComputeStatus('CONNECTING');
    try {
      const res = await connectToComputeRun({
        workspaceId,
        computeId: compute.computeId,
        fileId: urlState.notebookId,
        signal: abortControllerRef.current.signal
      });
      if (res.success && res.result.id && res.result.computeId && res.result.kernelId) {
        const {id, computeId, kernelId} = res.result;
        updateNotebookSessionId(id);
        updateNotebookComputeId(computeId);
        updateNotebookKernelId(kernelId);
        startSessionPolling(id, computeId);
      } else {
        clearConnection(pollRef.current!);
      }
    } catch (error) {
      console.error(error);
      clearConnection(pollRef.current!);
    }
  }

  const handleComputeClick = (compute: ComputeResourceItem) => {
    return (e: React.MouseEvent) => {
      if (disabledCompute(compute)) {
        e.stopPropagation();
        return;
      }
      const computeId = compute.computeId;
      if (computeId === activeCompute?.computeId) {
        return;
      }
      if (activeCompute) {
        Modal.confirm({
          title: '更换计算资源',
          content: '确定要切换计算资源吗？这将清除此notebook的状态。',
          onOk: async () => {
            disconnectFromComputeHandler(pollRef.current!);
            connectToComputeHandler(compute);
          }
        });
      } else {
        connectToComputeHandler(compute);
      }
    };
  };

  // 清理状态
  function clearConnection(poll: Poll) {
    resetNotebookStates();
    poll?.dispose();
  }

  // 渲染相关
  const renderComputeStatus = (compute: ComputeResourceItem) => {
    const statusObj = STATUS.fromValue(compute.status);
    return <div className={cx('status-icon', statusObj.className)}></div>;
  };

  const renderConnectStatus = () => {
    const icon = (
      <span
        className={cx('status-icon', {
          none: !activeCompute,
          connecting: connectStatus === 'CONNECTING'
        })}
      ></span>
    );
    return connectStatus === 'CONNECTED' ? renderComputeStatus(activeCompute) : icon;
  };

  const renderTag = (compute: ComputeResourceItem) => {
    const {engine, mirrorVersion} = compute;
    return <span className={cx('tag')}>{`${engine} ${mirrorVersion || ''}`}</span>;
  };

  const disabledCompute = (compute: ComputeResourceItem) => {
    return compute.status !== 'RUNNING';
  };

  const menu = (
    <div className={cx('compute-selector')}>
      {getComputeListLoading && <Loading loading size="small" />}
      {(connectStatus === 'CONNECTED' || connectStatus === 'CONNECTING') && activeCompute && (
        <div className={cx('compute-selected')}>
          <div className={cx('selected-action')}>
            {connectStatus === 'CONNECTED' && <span className={cx('connect-status')}>已连接</span>}
            {connectStatus === 'CONNECTING' && <span className={cx('connect-status')}>连接中</span>}
            <span
              className={cx('disconnect')}
              onClick={(e) => disconnectFromComputeHandler(pollRef.current!)}
            >
              断开连接
            </span>
          </div>
          <div className={cx('compute-item')} key={activeCompute.computeId}>
            <span className={cx('flex items-center')}>
              {renderConnectStatus()}
              <span className={cx('item-text')} title={activeCompute.name}>
                {activeCompute.name}
              </span>
            </span>
            {renderTag(activeCompute)}
          </div>
          <div className={cx('split-line')}></div>
        </div>
      )}
      <div className={cx('compute-list')}>
        <div className={cx('compute-list-title')}>常驻实例</div>
        {computeList.map((compute) => (
          <div
            className={cx('compute-item', {
              disabled: disabledCompute(compute)
            })}
            key={compute.computeId}
            onClick={handleComputeClick(compute)}
          >
            <span className={cx('flex items-center')}>
              {renderComputeStatus(compute)}
              <span className={cx('item-text')} title={compute.name}>
                {compute.name}
              </span>
            </span>
            {renderTag(compute)}
          </div>
        ))}
        <div className={cx('split-line')}></div>
      </div>
      {!isPrivate && (
        <div className={cx('compute-list')}>
          <div className={cx('compute-list-title')}>分析与AI搜索实例</div>
          {searchComputeList.map((compute) => (
            <div
              className={cx('compute-item', {
                disabled: disabledCompute(compute)
              })}
              key={compute.computeId}
              onClick={handleComputeClick(compute)}
            >
              <span className={cx('flex items-center')}>
                {renderComputeStatus(compute)}
                <span className={cx('item-text')} title={compute.name}>
                  {compute.name}
                </span>
              </span>
              {renderTag(compute)}
            </div>
          ))}
        </div>
      )}
      <AuthComponents tooltipType={TooltipType.Function} isAuth={hasComputeMenu}>
        <Button
          type="actiontext"
          onClick={() => {
            window.open(`#${urls.compute}?workspaceId=${workspaceId}`);
          }}
        >
          前往计算资源管理
        </Button>
      </AuthComponents>
    </div>
  );
  return (
    <div className={cx('compute-selector-dropdown')}>
      <Dropdown
        overlayStyle={{width: '320px'}}
        overlay={menu}
        trigger={['click']}
        placement="bottomRight"
        onVisibleChange={handleVisibleChange}
      >
        <div className={cx('compute-btn', 'acud-btn', 'acud-btn-default')}>
          {renderConnectStatus()}
          <Tooltip title={activeCompute?.name || '待连接'} placement="bottom">
            <span className={cx('item-text')}>{activeCompute?.name || '待连接'}</span>
          </Tooltip>
          <Arrow className={cx('arrow')} />
        </div>
      </Dropdown>
    </div>
  );
};

export default ComputeSelector;

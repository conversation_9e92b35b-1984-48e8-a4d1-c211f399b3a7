/**
 * 作业 定时任务 表单项
 * 提供列表页面 和 可视化 编辑界面 cron 表单项
 * 提供类型 配置
 */
import EditableContent from '@components/EditableContent';
import {CronItemTypeEnum, CronTypeChinese, CronTypeEnum, CronWeekArr} from '@pages/JobWorkflow/constants';
import {Checkbox, Form, Input, Select, TimePicker} from 'acud';
import {FormInstance} from 'acud/lib/form';
import CronExpressionParser from 'cron-parser';
import moment from 'moment';
import React from 'react';

const JobCronFormItem: React.FC<{
  disabled?: boolean;
  form: FormInstance;
  onChange?: (values: any, allValues: any) => void;
}> = ({disabled, form, onChange}) => {
  return (
    <>
      <Form.Item label="类型" name="type">
        <EditableContent isEditing={!disabled} dealValue={(value) => CronTypeChinese[value]}>
          <Select
            className="w-full"
            disabled={disabled}
            options={Object.values(CronTypeEnum).map((key) => ({
              label: CronTypeChinese[key],
              value: key
            }))}
            onSelect={() => {
              setTimeout(() => {
                form.setFieldsValue({
                  [CronItemTypeEnum.MINUTE]: 0,
                  [CronItemTypeEnum.TIME]: moment().hour(0).minute(0),
                  [CronItemTypeEnum.DAY]: [1],
                  [CronItemTypeEnum.WEEK]: [1],
                  [CronItemTypeEnum.MONTH]: [1],
                  [CronItemTypeEnum.OTHER]: '0 0 * * * ?'
                });
                onChange?.({}, form.getFieldsValue());
              }, 0);
            }}
          />
        </EditableContent>
      </Form.Item>

      <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}>
        {({getFieldValue}) => {
          const type = getFieldValue('type');

          switch (type) {
            case CronTypeEnum.HOUR:
              return (
                <Form.Item label="定时时间" name={CronItemTypeEnum.MINUTE}>
                  <EditableContent
                    isEditing={!disabled}
                    dealValue={(value) => (value < 10 ? '0' : '') + value.toString() + '分钟'}
                  >
                    <Select
                      clearIcon={false}
                      className="w-full"
                      disabled={disabled}
                      options={Array.from({length: 60}, (_, index) => ({
                        label: (index < 10 ? '0' : '') + index.toString() + '分钟',
                        value: index
                      }))}
                    />
                  </EditableContent>
                </Form.Item>
              );
            case CronTypeEnum.DAY:
              return (
                <Form.Item label="定时时间" name={CronItemTypeEnum.TIME}>
                  <EditableContent isEditing={!disabled} dealValue={(value) => value?.format('HH:mm')}>
                    <TimePicker disabled={disabled} format={'HH:mm'} clearIcon={false} className="w-full" />
                  </EditableContent>
                </Form.Item>
              );

            case CronTypeEnum.WEEK:
              return (
                <>
                  <Form.Item
                    label="星期"
                    name={CronItemTypeEnum.WEEK}
                    rules={[{required: true, message: '请选择星期'}]}
                  >
                    <EditableContent
                      isEditing={!disabled}
                      dealValue={(value) =>
                        value
                          ?.map((item) => CronWeekArr.find((item2) => item2.value === item)?.label)
                          ?.join(',')
                      }
                    >
                      <Checkbox.Group style={{flexWrap: 'wrap'}} options={CronWeekArr} />
                    </EditableContent>
                  </Form.Item>
                  <Form.Item label="定时时间" name={CronItemTypeEnum.TIME}>
                    <EditableContent isEditing={!disabled} dealValue={(value) => value?.format('HH:mm')}>
                      <TimePicker disabled={disabled} format={'HH:mm'} clearIcon={false} className="w-full" />
                    </EditableContent>
                  </Form.Item>
                </>
              );
            case CronTypeEnum.MONTH:
              return (
                <>
                  <Form.Item
                    label="日期选择"
                    name={CronItemTypeEnum.DAY}
                    rules={[{required: true, message: '请选择日期'}]}
                  >
                    <EditableContent
                      isEditing={!disabled}
                      dealValue={(value) => value.map((item) => (value < 10 ? '0' : '') + item).join(',')}
                    >
                      <Select
                        className="w-full"
                        mode="multiple"
                        disabled={disabled}
                        options={Array.from({length: 31}, (_, i) => i + 1).map((value) => ({
                          label: (value < 10 ? '0' : '') + value,
                          value
                        }))}
                      />
                    </EditableContent>
                  </Form.Item>
                  <Form.Item label="定时时间" name={CronItemTypeEnum.TIME}>
                    <EditableContent isEditing={!disabled} dealValue={(value) => value?.format('HH:mm')}>
                      <TimePicker disabled={disabled} format={'HH:mm'} clearIcon={false} className="w-full" />
                    </EditableContent>
                  </Form.Item>
                </>
              );
            case CronTypeEnum.YEAR:
              return (
                <>
                  <Form.Item
                    label="月份选择"
                    name={CronItemTypeEnum.MONTH}
                    rules={[{required: true, message: '请选择月份'}]}
                  >
                    <EditableContent
                      isEditing={!disabled}
                      dealValue={(value) => value.map((item) => (value < 10 ? '0' : '') + item).join(',')}
                    >
                      <Select
                        className="w-full"
                        mode="multiple"
                        disabled={disabled}
                        options={Array.from({length: 12}, (_, i) => i + 1).map((value) => ({
                          label: (value < 10 ? '0' : '') + value,
                          value
                        }))}
                      />
                    </EditableContent>
                  </Form.Item>
                  <Form.Item
                    label="日期选择"
                    name={CronItemTypeEnum.DAY}
                    rules={[{required: true, message: '请选择日期'}]}
                  >
                    <EditableContent
                      isEditing={!disabled}
                      dealValue={(value) => value.map((item) => (value < 10 ? '0' : '') + item).join(',')}
                    >
                      <Select
                        className="w-full"
                        mode="multiple"
                        disabled={disabled}
                        options={Array.from({length: 31}, (_, i) => i + 1).map((value) => ({
                          label: (value < 10 ? '0' : '') + value,
                          value
                        }))}
                      />
                    </EditableContent>
                  </Form.Item>
                  <Form.Item label="定时时间" name={CronItemTypeEnum.TIME}>
                    <EditableContent isEditing={!disabled} dealValue={(value) => value?.format('HH:mm')}>
                      <TimePicker disabled={disabled} format={'HH:mm'} clearIcon={false} className="w-full" />
                    </EditableContent>
                  </Form.Item>
                </>
              );
            case CronTypeEnum.OTHER:
              return (
                <Form.Item
                  label="CRON 表达式"
                  name={'crontab'}
                  // 自定义校验

                  rules={[
                    {required: !disabled ? true : false, message: '请输入CRON 表达式'},
                    {
                      validator: (_, value) => {
                        if (value) {
                          let interval;
                          try {
                            interval = CronExpressionParser.parse(value, {
                              strict: false
                            });
                          } catch (error) {
                            return Promise.reject(new Error('CRON表达式错误'));
                          }

                          // 校验最小间隔
                          const t1 = interval.next().getTime();
                          const t2 = interval.next().getTime();
                          const minSeconds = 60;
                          if (t2 - t1 < minSeconds * 1000) {
                            return Promise.reject(new Error('CRON表达式最小间隔为60秒'));
                          }
                        }

                        return Promise.resolve();
                      }
                    }
                  ]}
                >
                  <EditableContent isEditing={!disabled}>
                    <Input disabled={disabled} className="w-full" />
                  </EditableContent>
                </Form.Item>
              );
            default:
              return <>{type}</>;
          }
        }}
      </Form.Item>
    </>
  );
};

export default JobCronFormItem;

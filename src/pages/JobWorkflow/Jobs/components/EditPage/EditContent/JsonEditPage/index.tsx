import {IAppState} from '@store/index';
import {setFormIsDirty, setJson} from '@store/workflow';
import React, {useEffect, useState} from 'react';
import ReactMonacoEditor from 'react-monaco-editor';
import {useDispatch, useSelector} from 'react-redux';
import {jobData} from '../../globalVar';
import {useMemoizedFn} from 'ahooks';

/**
 * MonacoEditor编辑器组件
 */
const JsonEditPage: React.FC = () => {
  const jsonData = useSelector((state: IAppState) => state.workflowSlice.jsonData);
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const dispatch = useDispatch();
  const [value, setValue] = useState(jsonData);

  const handleChange = useMemoizedFn((value: string) => {
    setValue(value);
    jobData.value.code = value;
    dispatch(setJson(value));
    dispatch(setFormIsDirty(true));
  });

  // 导入更新json
  useEffect(() => {
    handleChange(jsonData);
  }, [jsonData, handleChange]);

  // 编辑器挂载完成 配置主题和语言规则
  const handleEditorDidMount = (editor, monaco) => {
    // 创建自定义主题
    monaco.editor.defineTheme('jsonTheme', {
      base: 'vs',
      inherit: true,
      rules: [
        {token: 'string.key.json', foreground: 'D97116'}, // JSON 键名
        {token: 'string.value.json', foreground: '1B9908'}, // JSON 字符串值
        {token: 'number.json', foreground: '2468F2'}, // JSON 数字
        {token: 'keyword.json', foreground: '8F5CFF'} // JSON 关键字（true, false, null）
      ],
      colors: {
        'editor.background': '#FBFBFC', // 编辑器背景色
        'minimap.background': '#F2F2F4', // 小地图背景色
        // 滚动条背景色
        'editorOverviewRuler.background': '#F2F2F4'
      }
    });

    // 应用主题
    monaco.editor.setTheme('logTheme');
  };

  return (
    <div
      style={{
        flex: '1',
        minWidth: 0, // 防止内容溢出
        height: '100%',
        display: 'flex'
      }}
    >
      <ReactMonacoEditor
        theme="jsonTheme"
        height="100%"
        language="json"
        value={value}
        editorDidMount={handleEditorDidMount}
        options={{
          readOnly: !isEditing,
          padding: {top: 8}, // ✅ 设置编辑器上边距为 8px
          scrollBeyondLastLine: false, // 关闭滚动超出最后一行
          automaticLayout: true,
          wordWrap: 'off',
          minimap: {
            enabled: true
          }
        }}
        onChange={(value) => {
          handleChange(value);
        }}
      />
    </div>
  );
};

export default JsonEditPage;

import {ComputeResourcePoolItem, getTaskInstanceTemplateList, TaskInstanceTemplate} from '@api/Compute';
import {STATUS} from '@pages/Compute/config';
import {WorkspaceContext} from '@pages/index';
import urls from '@utils/urls';
import {Button, Link, Select, Tag} from 'acud';
import React, {useContext, useEffect, useState} from 'react';

import {useMemoizedFn} from 'ahooks';
import styles from './index.module.less';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {TooltipType} from '@components/AuthComponents/constants';
import IconSvg from '@components/IconSvg';

// 任务运行参数
const ClusterIdSelectTemplate: React.FC = ({...rest}) => {
  const authList = useWorkspaceAuth([Privilege.ComputeMenu]);
  const hasComputeMenu = authList[Privilege.ComputeMenu];
  // 工作空间id
  const {workspaceId} = useContext(WorkspaceContext);
  const [clusterList, setClusterList] = useState<TaskInstanceTemplate[]>([]);

  const statusMap = {
    [String(STATUS.DEPLOY)]: 'status-active',
    [String(STATUS.RUNNING)]: 'status-success',
    [String(STATUS.INVALID)]: 'status-error',
    [String(STATUS.CREATED_FAIL)]: 'status-error'
  };
  // 搜索计算集群
  const getClusterList = useMemoizedFn(async () => {
    console.log('searchClusterList');
    const res = await getTaskInstanceTemplateList({
      workspaceId: workspaceId,
      pageNo: 1,
      pageSize: 10000
    });
    // 过滤出当前需要类型
    setClusterList(res.result?.templates);
  });

  useEffect(() => {
    getClusterList();
  }, []);

  return (
    <>
      <Select
        {...rest}
        className="w-full "
        placeholder="请选择计算集群"
        showSearch
        allowClear
        onDropdownVisibleChange={getClusterList}
        dropdownMatchSelectWidth={false}
        dropdownRender={(menu) => (
          <>
            {menu}
            <div className={styles['cluster-footer']}>
              <AuthComponents tooltipType={TooltipType.Function} isAuth={hasComputeMenu}>
                <Link
                  onClick={() =>
                    window.open(
                      `${window.location.pathname}#${urls.compute}?workspaceId=${workspaceId}&?tab=dataProcess&secondTab=Template`,
                      '_blank'
                    )
                  }
                >
                  任务实例模版列表
                </Link>
              </AuthComponents>
            </div>
          </>
        )}
      >
        {clusterList?.map((item) => (
          <Select.Option key={item.id} value={item.id}>
            <div className={styles['cluster-item']}>
              <Tag color="transparent">{item.name}</Tag>
              {/* <Button
                icon={<IconSvg type="edit" />}
                type="actiontext"
                onClick={() => {
                  window.open(
                    `${window.location.pathname}#${urls.computeCreateTaskInstance}?workspaceId=${workspaceId}&templateId=${item.id}&isEdit=true`,
                    '_blank'
                  );
                }}
              ></Button> */}
            </div>
          </Select.Option>
        ))}
      </Select>
    </>
  );
};

export default ClusterIdSelectTemplate;

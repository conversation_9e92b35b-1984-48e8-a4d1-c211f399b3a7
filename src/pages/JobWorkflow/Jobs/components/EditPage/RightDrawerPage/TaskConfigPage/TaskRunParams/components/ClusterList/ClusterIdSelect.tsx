import {ComputeResourceItem, getComputeResourceList} from '@api/Compute';
import {STATUS} from '@pages/Compute/config';
import {WorkspaceContext} from '@pages/index';
import urls from '@utils/urls';
import {Link, Select, Tag} from 'acud';
import React, {useContext, useEffect, useState} from 'react';

import {EngineTypeEnum} from '@pages/JobWorkflow/constants';
import {useMemoizedFn} from 'ahooks';
import styles from './index.module.less';

import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {TooltipType} from '@components/AuthComponents/constants';

// 任务运行参数
const EngineTypeSelect: React.FC<{
  engineType: EngineTypeEnum;
}> = ({engineType, ...rest}) => {
  const authList = useWorkspaceAuth([Privilege.ComputeMenu]);
  const hasComputeMenu = authList[Privilege.ComputeMenu];
  // 工作空间id
  const {workspaceId} = useContext(WorkspaceContext);
  const [clusterList, setClusterList] = useState<ComputeResourceItem[]>([]);

  const statusMap = {
    [String(STATUS.DEPLOY)]: 'status-active',
    [String(STATUS.RUNNING)]: 'status-success',
    [String(STATUS.INVALID)]: 'status-error',
    [String(STATUS.CREATED_FAIL)]: 'status-error'
  };
  // 搜索计算集群
  const getClusterList = useMemoizedFn(async () => {
    console.log('searchClusterList');
    const res = await getComputeResourceList({
      workspaceId: workspaceId,
      pageNo: 1,
      pageSize: 10000
    });
    // 过滤出当前需要类型
    setClusterList(res.result?.computes);
  });

  useEffect(() => {
    getClusterList();
  }, []);

  return (
    <>
      <Select
        {...rest}
        className="w-full "
        placeholder="请选择计算集群"
        showSearch
        allowClear
        onDropdownVisibleChange={getClusterList}
        dropdownMatchSelectWidth={false}
        dropdownRender={(menu) => (
          <>
            {menu}
            <div className={styles['cluster-footer']}>
              前往
              <AuthComponents tooltipType={TooltipType.Function} isAuth={hasComputeMenu}>
                <Link
                  onClick={() =>
                    window.open(
                      `${window.location.pathname}#${urls.compute}?workspaceId=${workspaceId}`,
                      '_blank'
                    )
                  }
                >
                  计算资源管理
                </Link>
              </AuthComponents>
            </div>
          </>
        )}
      >
        {clusterList
          ?.filter((item) => item.engine?.toLowerCase() === engineType?.toLowerCase())
          ?.map((item) => (
            <Select.Option
              key={item.computeId}
              value={item.computeId}
              disabled={item.status !== STATUS.RUNNING}
            >
              <div className={styles['cluster-item']}>
                <Tag color="transparent" icon={<span className={`circle ${statusMap[item.status]}`}></span>}>
                  {item.name}
                </Tag>
                <Tag>{`${item.engine} ${item.mirrorVersion || ''}`}</Tag>
              </div>
            </Select.Option>
          ))}
      </Select>
    </>
  );
};

export default EngineTypeSelect;

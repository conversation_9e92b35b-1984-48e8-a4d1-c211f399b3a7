import {detailJob, queryJobList} from '@api/job';
import {queryWorkspaceDetail, queryWorkspaceList} from '@api/workspace';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import RemoteSelect from '@components/RemoteSelect';
import {
  JobDependencyCycleChineseMap,
  JobDependencyTypeChineseMap,
  JobDependencyTypeEnum
} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {Form, Select} from 'acud';
import {FormInstance} from 'acud/lib/form';
import {useMemoizedFn} from 'ahooks';
import React, {useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import {IJsonNodeData} from '../../../../EditContent/X6EditPage/type';
import {nodeMap} from '../../../../globalVar';
import DependencyTaskParamsDateValue from './formItem/DependencyTaskParamsDateValue';
import {Privilege} from '@api/permission/type';
import flags from '@/flags';
import SelectWorkflowNormal from './DependencyTaskParams/SelectWorkflowNormal';
import SelectWorkflowPrivate from './DependencyTaskParams/SelectWorkflowPrivate';

const DependencyTaskParams: React.FC<{form: FormInstance}> = ({form}) => {
  const isDBPrivate = flags.DatabuilderPrivateSwitch;

  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);

  const [initTime, setInitTime] = useState<number>(new Date().getTime());

  return (
    <>
      {isDBPrivate ? <SelectWorkflowPrivate form={form} /> : <SelectWorkflowNormal form={form} />}

      <Form.Item
        label="检查周期"
        name="cycle"
        tooltip="时间范围内，按小时/天/周/月周期检查依赖的任务是否成功。示例：任务B对任务A的依赖时，依赖检查组件的时间周期设置为按小时级，且范围为前3小时。任务 B 在每次执行前，依赖检查组件会检查任务 A 在过去三小时内每个小时对应的任务实例（共三个）是否都已成功执行。如果这三个实例都成功完成，任务 B 就会开始执行；如果有任何一个实例未完成或执行失败，任务 B 就会等待或者失败"
      >
        <EditableContent isEditing={isEditing} dealValue={(v) => JobDependencyCycleChineseMap[v]}>
          <Select
            className="w-full"
            onSelect={(v) => {
              setInitTime(new Date().getTime());
            }}
            options={Object.entries(JobDependencyCycleChineseMap).map(([key, value]) => ({
              label: value,
              value: key
            }))}
          />
        </EditableContent>
      </Form.Item>
      <Form.Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.cycle !== curValues.cycle}>
        {({getFieldValue}) => {
          return (
            <Form.Item label="  " name="dateValue">
              <DependencyTaskParamsDateValue type={getFieldValue('cycle')} initTime={initTime} />
            </Form.Item>
          );
        }}
      </Form.Item>
    </>
  );
};

export default DependencyTaskParams;

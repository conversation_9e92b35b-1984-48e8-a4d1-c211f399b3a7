/**
 * 工作流 常量
 * 记录工作流相关常量 包括枚举 常量 类型 等
 * <AUTHOR>
 */

import flags from '@/flags';
import {IOperCategoryEnum} from '@api/metaRequest';

const isPrivate = flags.DatabuilderPrivateSwitch;

/** 页面类型 */
export enum WorkflowPageTypeEnum {
  JOBS = 'jobs',
  TEMPLATES = 'templates'
}

// 页面类型 详情/实例
export enum JobDetailPageTypeEnum {
  DETAIL = 'detail',
  JOB_INSTANCES = 'jobInstances'
}

/** 页面类型 */
export enum JobCreateTypeEnum {
  // 新建空任务
  EMPTY = 'empty',
  // 新建 JSON 任务
  JSON = 'json'
}
/** 页面展示类型 */
export enum JobShowTypeEnum {
  // 可视化
  X6 = 'x6',
  // JSON
  JSON = 'json'
}
/** 节点类型 */
export enum JobNodeTypeEnum {
  // ray 任务
  RAY_TASK = 'RAY_TASK',
  // 算子任务
  DATAFLOW_TASK = 'DATAFLOW_TASK',
  // 训练任务
  AIHC_TASK = 'AIHC_TASK',
  // 集成任务
  FILE_INTEGRATION_TASK = 'FILE_INTEGRATION_TASK',
  // 结构化任务
  TABLE_INTEGRATION_TASK = 'TABLE_INTEGRATION_TASK',
  // 依赖任务
  DEPENDENT_TASK = 'DEPENDENT_TASK',
  // notebook 任务
  NOTEBOOK_TASK = 'NOTEBOOK_TASK',
  // SparkJar 任务
  SPARK_JAR_TASK = 'SPARK_TASK',
  // 算子节点
  OPERATOR_NODE = 'OPERATOR_NODE'
}

// 任务节点类型
export const JobTaskType = {
  [JobNodeTypeEnum.DATAFLOW_TASK]: {
    label: '算子任务',
    value: JobNodeTypeEnum.DATAFLOW_TASK,
    icon: 'workflow-type-dataflow-task',
    color: '#3399FF'
  },
  [JobNodeTypeEnum.RAY_TASK]: {
    label: 'Ray任务',
    value: JobNodeTypeEnum.RAY_TASK,
    icon: 'workflow-type-ray-task',
    color: '#3399FF'
  },
  [JobNodeTypeEnum.NOTEBOOK_TASK]: {
    label: 'NoteBook',
    value: JobNodeTypeEnum.NOTEBOOK_TASK,
    icon: 'workflow-type-notebook-task',
    color: '#3399FF'
  },
  [JobNodeTypeEnum.DEPENDENT_TASK]: {
    label: '依赖检查',
    value: JobNodeTypeEnum.DEPENDENT_TASK,
    icon: 'workflow-type-dependent-task',
    color: '#3399FF'
  },
  [JobNodeTypeEnum.FILE_INTEGRATION_TASK]: {
    label: '文件采集',
    value: JobNodeTypeEnum.FILE_INTEGRATION_TASK,
    icon: 'workflow-type-file-task',
    color: '#3399FF'
  },
  ...(isPrivate
    ? {}
    : {
        [JobNodeTypeEnum.TABLE_INTEGRATION_TASK]: {
          label: '库表采集',
          value: JobNodeTypeEnum.TABLE_INTEGRATION_TASK,
          icon: 'workflow-type-batch-task',
          color: '#3399FF'
        }
      }),
  [JobNodeTypeEnum.SPARK_JAR_TASK]: {
    label: 'SparkJar任务',
    value: JobNodeTypeEnum.SPARK_JAR_TASK,
    icon: 'workflow-type-spark-jar-task',
    color: '#3399FF'
  },

  [JobNodeTypeEnum.AIHC_TASK]: {
    label: '训练任务',
    value: JobNodeTypeEnum.AIHC_TASK,
    icon: 'workflow-type-train',
    color: '#3399FF'
  }
};
/**
 * 节点类型
 * 1. 边
 * 2. 任务节点
 * 3. 算子节点
 */

export enum X6ShapeTypeEnum {
  EDGE = 'edge',
  TASK = 'task-node',
  TASK_EDGE = 'task-edge',
  OPERATOR = 'operator-node',
  OPERATOR_EDGE = 'operator-edge',
  OPERATOR_GROUP = 'operator-group-node'
}

/** 页面弹窗配置类型 */
export enum JobModalTypeEnum {
  // 新建 cron
  CRON = 'cron',
  // 新建 运行实例
  RUN_INSTANCE = 'runInstance',

  // 新建空任务
  EMPTY = 'empty',
  // 新建 JSON 任务
  JSON = 'json'
}

/** cron 类型 */
export enum CronTypeEnum {
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
  OTHER = 'other'
}

/** cron 类型中文 */
export const CronTypeChinese = {
  [CronTypeEnum.HOUR]: '每小时',
  [CronTypeEnum.DAY]: '每天',
  [CronTypeEnum.WEEK]: '每周',
  [CronTypeEnum.MONTH]: '每月',
  [CronTypeEnum.YEAR]: '每年',
  [CronTypeEnum.OTHER]: 'CRON 表达式'
};

/** 任务调度状态 */
export enum JobScheduleStatusEnum {
  ON = 'ON',
  OFF = 'OFF',
  PENDING = 'PENDING'
}

/** 任务调度状态中文 */
export const JobScheduleStatusChinese = {
  [JobScheduleStatusEnum.ON]: '开启',
  [JobScheduleStatusEnum.OFF]: '关闭',
  [JobScheduleStatusEnum.PENDING]: '等待中'
};

/** cron 表单 key */
export enum CronItemTypeEnum {
  TIME = 'time',
  MINUTE = 'minute',
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  OTHER = 'crontab'
}

// 星期
export const CronWeekArr = [
  {
    value: 1,
    label: '周一'
  },
  {
    value: 2,
    label: '周二'
  },
  {
    value: 3,
    label: '周三'
  },
  {
    value: 4,
    label: '周四'
  },
  {
    value: 5,
    label: '周五'
  },
  {
    value: 6,
    label: '周六'
  },
  {
    value: 7,
    label: '周日'
  }
];

/** 创建 类型 */
export enum WorkflowPageCreateTypeEnum {
  COPY = 'copy',
  TEMPLATE = 'template'
}

// 节点大小 位置
export const NODE_SIZE = {
  [X6ShapeTypeEnum.TASK]: {
    width: 240,
    height: 44,
    // 任务节点 上下左右 预留间距
    padding: {top: 60, bottom: 30, left: 30, right: 30},
    // 任务节点 上下间距
    ranksep: 30,
    // 任务节点 左右间距
    nodesep: 20,
    titleHeight: 52,
    gutter: 60,
    zIndex: 1
  },
  [X6ShapeTypeEnum.OPERATOR]: {
    width: 176,
    height: 32,
    // 算子节点 上下间距
    ranksep: 8,
    // 算子节点 左右间距
    nodesep: 10,
    gutter: 24,
    paddingLeft: 12,
    zIndex: 4
  },
  [X6ShapeTypeEnum.OPERATOR_GROUP]: {
    padding: 12,
    titleHeight: 24,
    zIndex: 2
  },
  [X6ShapeTypeEnum.TASK_EDGE]: {
    zIndex: 0
  },
  [X6ShapeTypeEnum.OPERATOR_EDGE]: {
    zIndex: 3
  }
};

/** 图表配置 */
export const GraphOptions = {
  zoomToFit: {
    padding: 50,
    maxScale: 1,
    minScale: 0.5
  }
};

// 右侧展示类型
export enum RightDrawerTypeEnum {
  INIT = 'init',
  JOB_CONFIG = 'jobConfig',
  TASK_CONFIG = 'taskConfig',
  OPERATOR_CONFIG = 'operatorConfig'
}

// 左侧拖拽类型
export enum LeftDragTypeEnum {
  DEFAULT = 'default',
  OPERATOR = 'operator'
}

// 选中节点类型
export enum SelectJobNodeTypeEnum {
  // 空
  NULL = 'null',
  // 点击
  CLICK = 'click',
  // 搜索
  SEARCH = 'search',
  // 编辑 算子
  EDIT_OPERATOR = 'editOperator',
  // 保存 算子
  SAVE_OPERATOR = 'saveOperator',
  // 复制
  COPY = 'copy',
  // 删除
  DELETE = 'delete'
}

// 分隔符
export const SPLIT_STR = '-';

// json 格式化
export const JSON_FORMAT = 2;

// 缩放按钮类型
export enum X6BtnArrTypeEnum {
  AUTO_LAYOUT = 'autoLayout',
  FOCUS_CENTER = 'focusCenter'
}

// 算子 类别 中文
export const OperatorCategoryChineseMap = {
  [IOperCategoryEnum.Dedup]: '去重',
  [IOperCategoryEnum.Embedding]: '嵌入',
  [IOperCategoryEnum.Extract]: '抽取',
  [IOperCategoryEnum.Filter]: '过滤',
  [IOperCategoryEnum.Transform]: '处理',
  [IOperCategoryEnum.Source]: '输入',
  [IOperCategoryEnum.Sink]: '输出',
  [IOperCategoryEnum.Others]: '其他'
};
// 算子 过滤类型
export enum OperatorFieldEnum {
  TEXT = 'Text',
  IMAGE = 'Image',
  AUDIO = 'Audio',
  VIDEO = 'Video',
  MULTIMODAL = 'Multimodal',
  GENERAL = 'General'
}
// 算子 过滤类型 中文
export const OperatorFieldChineseMap = {
  [OperatorFieldEnum.TEXT]: '文本',
  [OperatorFieldEnum.IMAGE]: '图片',
  [OperatorFieldEnum.AUDIO]: '音频',
  [OperatorFieldEnum.VIDEO]: '视频',
  [OperatorFieldEnum.MULTIMODAL]: '多模态',
  [OperatorFieldEnum.GENERAL]: '通用'
};

/**
 * 可视化页面类型(用于区分 结果页面/详情页面)
 * 1. 编辑页面 可点击 可拖拽 可编辑 （还可以切换只读+点击）
 * 2. 结果页面 可点击 不可编辑
 * 3. 详情页面 不可点击 不可编辑 只展示
 * TODO 补数据 可多选节点
 */
export enum X6PageTypeEnum {
  JOB_EDIT = 'jobEdit',
  JOB_RESULT = 'jobResult',
  JOB_DETAIL = 'jobDetail'
}

// 依赖检查 失败策略
export enum JobDependencyFailurePolicyEnum {
  DEPENDENT_FAILURE_WAITING = 'DEPENDENT_FAILURE_WAITING',
  DEPENDENT_FAILURE_FAILURE = 'DEPENDENT_FAILURE_FAILURE'
}

// 依赖检查 失败策略 中文
export const JobDependencyFailurePolicyChineseMap = {
  [JobDependencyFailurePolicyEnum.DEPENDENT_FAILURE_WAITING]: '等待',
  [JobDependencyFailurePolicyEnum.DEPENDENT_FAILURE_FAILURE]: '失败'
};

// 依赖检查 周期
export enum JobDependencyCycleEnum {
  DAILY = 'day',
  HOURLY = 'hour',
  WEEKLY = 'week',
  MONTHLY = 'month'
}

// 依赖检查 周期 中文
export const JobDependencyCycleChineseMap = {
  [JobDependencyCycleEnum.HOURLY]: '小时级',
  [JobDependencyCycleEnum.DAILY]: '天级',
  [JobDependencyCycleEnum.WEEKLY]: '周级',
  [JobDependencyCycleEnum.MONTHLY]: '月级'
};

// 依赖类型
export enum JobDependencyTypeEnum {
  DEPENDENT_JOB = 'DEPENDENT_JOB',
  DEPENDENT_TASK = 'DEPENDENT_TASK'
}

export const JobDependencyTypeChineseMap = {
  [JobDependencyTypeEnum.DEPENDENT_JOB]: '依赖工作流',
  [JobDependencyTypeEnum.DEPENDENT_TASK]: '依赖任务'
};
// 计算引擎类型
export enum EngineTypeEnum {
  RAY = 'RAY',
  SPARK = 'SPARK',
  DORIS = 'DORIS'
}
// 集群类型
export enum ClusterTypeEnum {
  RESIDENT = 'RESIDENT',
  EPHEMERAL = 'EPHEMERAL'
}
// 计算引擎类型
export const ClusterTypeChineseMap = {
  [ClusterTypeEnum.RESIDENT]: '常驻集群',
  [ClusterTypeEnum.EPHEMERAL]: '任务实例'
};
// spark 配置对象
export const SparkConfObj = {
  'spark.driver.cores': 1,
  'spark.driver.memory': '1g',
  'spark.executor.cores': 1,
  'spark.executor.instances': 1,
  'spark.executor.memory': '1g'
};

// 白舸 数据源 类型
export const AihcTaskDataSourcesType = {
  BOS: 'BOS',
  PFS: 'PFS'
};

import useUrlState from '@ahooksjs/use-url-state';
import {createIntegrationJob, getJobDetails, updateIntegrationJob} from '@api/integration';
import {BatchObj, getDatasourceTableColumns} from '@api/integration/batch';
import {JobType} from '@api/integration/type';
import IconSvg from '@components/IconSvg';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {WorkspaceContext} from '@pages/index';
import {Button, Form, Select, Space, Steps, toast} from 'acud';
import {useMemoizedFn} from 'ahooks';
import classNames from 'classnames/bind';
import React, {useContext, useEffect, useState} from 'react';
import {
  DirtyDataStrategyEnum,
  SinkModeEnum,
  SinkNameRuleEnum,
  SinkTableTypeEnum,
  SinkTypeEnum,
  SourceChangeHandleEnum,
  SourceConfigEnum,
  SourceTypeEnum,
  SqlCollectTabEnum,
  TargetTypeEnum
} from '../../constants';
import styles from './index.module.less';
import MapSetting from './page/MapSetting';
import RuntimeInfo from './page/RuntimeInfo';
import SourceSink from './page/SourceSink';
import {isBoolean, set} from 'lodash';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import {
  checkMapping,
  checkPartitioningTable,
  checkPartitioningTableName,
  checkTargetTable,
  initMapping
} from './page/MapSetting/utils';
import LeaveFromModal from '@components/LeaveFromModal';

const {Step} = Steps;
const {Option} = Select;

const cx = classNames.bind(styles);
const stepItems = [{title: '源端与目标端配置'}, {title: '运行信息配置'}, {title: '映射配置'}];

/**
 * 离线同步 - 编辑任务
 */
const OfflineCollectConfig: React.FC = () => {
  // 离开时间
  const [leaveClickTime, setLeaveClickTime] = useState(0);
  const [isEditing, setIsEditing] = useState(false);

  const navigate = useNavigate();
  /** 是否是编辑任务 */
  const [{jobId, sourceType, sourceConnectionId}] = useUrlState();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [taskName, setTaskName] = useState('');

  const [form] = Form.useForm();

  const {workspaceId} = useContext(WorkspaceContext);

  // 下一步
  const handleNextStep = useMemoizedFn(async () => {
    try {
      const values = await form.validateFields();
      console.log(values);

      if (currentStep === 0) {
        const res = await initMapping(workspaceId, form.getFieldsValue());
        form.setFieldsValue(res);
      }
      setCurrentStep((prev) => prev + 1);
    } catch (error) {
      console.log(error);
      toast.error({message: '请检查表单是否正确'});
    }
  });

  // 上一步
  const handlePrevStep = useMemoizedFn(() => {
    setCurrentStep((prev) => prev - 1);
  });

  // 保存
  const handleSubmit = useMemoizedFn(async () => {
    try {
      // 校验表单
      await form.validateFields();
    } catch {
      toast.error({message: '请检查表单是否正确'});
      setCurrentStep(0);
      return false;
    }

    setLoading(true);

    let values = form.getFieldsValue();
    if (
      !checkTargetTable(values[SourceConfigEnum.MappingConfig].sinkFields) ||
      !checkPartitioningTable(values[SourceConfigEnum.MappingConfig].sinkPartitions)
    ) {
      toast.error({message: '请检查目标端字段是否正确'});
      setCurrentStep(2);
      return;
    }

    if (!checkMapping(values[SourceConfigEnum.MappingConfig].mapping)) {
      toast.error({message: '请检查映射关系存在'});
      setCurrentStep(2);
      return;
    }

    values = await initMapping(workspaceId, values);
    setIsEditing(false);
    // 暂时只支持批量任务
    values.type = JobType.Batch;
    // 删除源端表和目标表全名
    delete values.sourceTable;
    delete values.sinkFullName;
    let res;
    if (jobId) {
      res = await updateIntegrationJob(workspaceId, jobId, values);
    } else {
      res = await createIntegrationJob(workspaceId, values);
    }
    setLoading(false);

    if (res.success) {
      toast.success({message: '保存成功', duration: 5});
      navigate(`${urls.integration}?tab=${SqlCollectTabEnum.Offline}`);
      return true;
    } else {
      setIsEditing(true);
      return false;
      // toast.error({message: '保存失败'});
    }
  });

  // 取消
  const handleCancel = useMemoizedFn(() => {
    setLeaveClickTime(Date.now());
    navigate(`${urls.integration}?tab=${SqlCollectTabEnum.Offline}`);
  });

  /** 初始化 */
  const initFn = useMemoizedFn(async () => {
    const res = await getJobDetails(workspaceId, jobId);
    const obj: any = res.result;
    setTaskName(obj.name);
    form.setFieldsValue(obj);
  });

  useEffect(() => {
    if (jobId) {
      initFn();
    } else {
      form.setFieldsValue({
        type: JobType.Batch,
        workspaceId: workspaceId,
        [SourceConfigEnum.SourceConfig]: {
          enableRateLimit: false,
          sourceType: sourceType || SourceTypeEnum.MySQL,
          sourceConnectionId,
          rateLimit: {
            flow: 1,
            records: 100000
          },
          parallelism: 1,
          sourceChange: {
            onDeleteSource: SourceChangeHandleEnum.PAUSE,
            onDeleteColumn: SourceChangeHandleEnum.SKIP,
            onAddColumn: SourceChangeHandleEnum.SKIP
          },
          toleranceType: SinkModeEnum.OVERWRITE,
          enableDirtyDataWrite: false
        },
        [SourceConfigEnum.SinkConfig]: {
          sinkMode: SinkModeEnum.OVERWRITE,
          target: TargetTypeEnum.DataBuilderCatalog,
          sinkType: SinkTypeEnum.iceberg,
          isAutoCreated: true,
          sinkTableType: SinkTableTypeEnum.MANAGED,
          sinkNameRule: SinkNameRuleEnum.SAME,
          sinkPath: '',
          dirtyDataStrategy: {
            strategy: DirtyDataStrategyEnum.STRICT,
            enableDirtyDataWrite: false
          }
        }
      });
    }
  }, [jobId, sourceType]);

  return (
    <div className={styles['sql-collect-config']}>
      <div className={styles['config-title']} onClick={handleCancel}>
        <IconSvg type="left" size={16} className="mr-[12px]" />
        {jobId ? (
          <TextEllipsis tooltip={'编辑' + taskName}>{'编辑' + taskName || '-'}</TextEllipsis>
        ) : (
          '创建库表离线采集'
        )}
      </div>
      <div className={styles['config-container']}>
        <div className={styles['config-step']}>
          <Steps current={currentStep} size="small">
            {stepItems.map((item) => (
              <Step key={item.title} title={item.title} />
            ))}
          </Steps>
        </div>
        <div className={styles['config-form']}>
          <Form
            onValuesChange={() => setIsEditing(true)}
            form={form}
            labelWidth="80px"
            labelAlign="left"
            inputMaxWidth={900}
            colon={false}
          >
            <Form.Item name="workspaceId" noStyle hidden></Form.Item>
            {/* 源端字段 暂时只有前端使用 */}
            <Form.Item name="sourceTable" noStyle hidden></Form.Item>
            <Form.Item name="sinkFullName" noStyle hidden></Form.Item>

            <div
              className={styles['config-form-item']}
              style={{display: currentStep === 0 ? 'block' : 'none'}}
            >
              <SourceSink form={form} isEdit={!!jobId} currentStep={currentStep} />
            </div>
            <div
              className={styles['config-form-item']}
              style={{display: currentStep === 1 ? 'block' : 'none'}}
            >
              <RuntimeInfo form={form} disabled={false} />
            </div>
            <div
              className={styles['config-form-item']}
              style={{display: currentStep === 2 ? 'block' : 'none'}}
            >
              <MapSetting form={form} currentStep={currentStep} />
            </div>
          </Form>
        </div>
      </div>
      <div className={styles['config-footer']}>
        <Space>
          {currentStep !== 0 && (
            <Button type="default" onClick={handlePrevStep}>
              上一步
            </Button>
          )}
          {currentStep !== 2 && (
            <Button type="primary" onClick={handleNextStep}>
              下一步
            </Button>
          )}
          <Button type="default" onClick={handleSubmit}>
            保存
          </Button>
          <Button type="default" onClick={handleCancel}>
            取消
          </Button>
        </Space>
      </div>
      <LeaveFromModal
        leaveClickTime={leaveClickTime}
        isEditing={isEditing}
        onSave={handleSubmit}
        title="当前数据暂未保存，如果现在关闭，未保存的数据内容将丢失。请确认是否要继续关闭"
      ></LeaveFromModal>
    </div>
  );
};

export default OfflineCollectConfig;

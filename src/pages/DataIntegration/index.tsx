import React, {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import useUrlState from '@ahooksjs/use-url-state';
import {Loading, Tabs} from 'acud';
import classNames from 'classnames/bind';
import {IntegrationTab} from './constants';
import {FileCollectList} from './FileCollect/FileCollectList';

import {getIntegrationJobList} from '@api/integration';
import urls from '@utils/urls';
import {useNavigate} from 'react-router-dom';
import {WorkspaceContext} from '..';
import {IntegrationGuide} from './components/GuidePage';
import CreateFileCollectModal from './FileCollect/components/CreateFileCollectModal';
import styles from './index.module.less';
import SqlCollect from './SqlCollect';
import OnOpenModal, {ModalTypeEnum} from './SqlCollect/OfflineCollect/components/OnOpenModal';
import * as http from '@api/integration';
import {JobType, FileSourceType} from '@api/integration/type';
import flags from '@/flags';

const {TabPane} = Tabs;

const cx = classNames.bind(styles);

const isPrivate = flags.DatabuilderPrivateSwitch;

/**
 * 数据集成
 */
const IntegrationPage: React.FC = () => {
  const navigate = useNavigate();
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState, setUrlState] = useUrlState<{tab: IntegrationTab}>({
    tab: IntegrationTab.FileCollect
  });
  const [isEmpty, setIsEmpty] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [createFileVisible, setCreateFileVisible] = useState<boolean>(false);

  const onCancelCreateModal = () => setCreateFileVisible(false);

  const onChange = (activeKey: string) => {
    setUrlState({tab: activeKey});
  };

  // 创建弹窗-跳转到不同模块的路径下
  const createModal = (key: IntegrationTab) => {
    key === IntegrationTab.FileCollect && setCreateFileVisible(true);
    if (key === IntegrationTab.Offline) {
      OnOpenModal(ModalTypeEnum.Create, [], '', () => {}, navigate);
    }
  };

  const tabsList = useMemo(
    () => [
      {
        label: '文件离线采集',
        key: IntegrationTab.FileCollect,
        render: <FileCollectList createModal={() => createModal(IntegrationTab.FileCollect)} />
      },
      ...(isPrivate
        ? []
        : [
            {
              label: '库表离线采集',
              key: IntegrationTab.Offline,
              render: <SqlCollect />
            }
          ])
    ],
    []
  );

  // 获取任务列表
  const getJobList = useCallback(async () => {
    setLoading(true);
    try {
      Promise.all([
        getIntegrationJobList(workspaceId, {
          pageNo: 1,
          pageSize: 10,
          type: JobType.File
        }),
        getIntegrationJobList(workspaceId, {
          pageNo: 1,
          pageSize: 10,
          type: JobType.Batch
        })
        //getIntegrationJobList(workspaceId, {
        // pageNo: 1,
        //  pageSize: 10,
        // type: JobType.CDC
        //})
      ]).then(([res1, res2]) => {
        const totalCount = (res1.result?.totalCount ?? 0) + (res2.result?.totalCount ?? 0);

        setIsEmpty(totalCount === 0);
      });
    } catch {
      console.error('获取文件采集任务列表失败');
    } finally {
      setLoading(false);
    }
  }, [workspaceId]);

  useEffect(() => {
    getJobList();
  }, [getJobList]);

  // 创建文件采集成功后跳转到列表页
  const updateFileList = () => {
    setIsEmpty(false);
    setUrlState({tab: IntegrationTab.FileCollect});
  };

  // 创建文件采集成功回调
  const onCreateFileCollectOk = (dataSource: FileSourceType) => {
    navigate(`${urls.fileCollectCreate}?sourceType=${dataSource}`);
  };

  return (
    <div className={cx('db-workspace-wrapper', 'integration-page')}>
      <div className={cx('title')}>数据集成</div>
      <Loading loading={loading} />
      {isEmpty ? (
        <IntegrationGuide createModal={createModal} />
      ) : (
        <Tabs onChange={onChange} activeKey={urlState.tab} destroyInactiveTabPane>
          {tabsList.map((item) => (
            <TabPane tab={item.label} key={item.key}>
              {item.render}
            </TabPane>
          ))}
        </Tabs>
      )}
      <CreateFileCollectModal
        visible={createFileVisible}
        onOk={onCreateFileCollectOk}
        onCancel={onCancelCreateModal}
        updateFileList={updateFileList}
      />
    </div>
  );
};

export default IntegrationPage;

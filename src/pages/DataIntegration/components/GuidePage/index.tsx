import {IntegrationTab} from '@pages/DataIntegration/constants';
import {But<PERSON>} from 'acud';
import classNames from 'classnames/bind';
import React from 'react';

import styles from './index.module.less';
import {Privilege} from '@api/permission/type';
import AuthButton from '@components/AuthComponents/AuthButton';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {TooltipType} from '@components/AuthComponents/constants';
import flags from '@/flags';

const cx = classNames.bind(styles);

interface IntegrationGuideProps {
  // 创建弹窗
  createModal: (key: IntegrationTab) => void;
}

const isPrivate = flags.DatabuilderPrivateSwitch;

// 空状态引导页
export const IntegrationGuide: React.FC<IntegrationGuideProps> = ({createModal}) => {
  const permission = useWorkspaceAuth([
    Privilege.UnstructuredIntegrationCreate,
    Privilege.StructuredIntegrationCreate
  ]);
  const guideConfig = [
    {
      title: '创建文件离线采集',
      key: IntegrationTab.FileCollect,
      description: '适用于定时采集文件到卷',
      disabled: false,
      icon: 'file-collect',
      privilege: Privilege.UnstructuredIntegrationCreate
    },
    ...(isPrivate
      ? []
      : [
          {
            title: '创建库表离线采集',
            key: IntegrationTab.Offline,
            description: '适用于定时采集数据库表',
            disabled: false,
            icon: 'offline',
            privilege: Privilege.StructuredIntegrationCreate
          },
          {
            title: '创建库表实时同步',
            key: IntegrationTab.Realtime,
            description: '适用于实时采集数据库表',
            disabled: true,
            icon: 'realtime'
          }
        ])
  ];

  return (
    <div className={cx('guide')}>
      {guideConfig.map((item, index) => (
        <div key={index} className={cx('guide-item')}>
          <div className={cx('guide-icon', `${item.icon}`)}></div>
          <div className={cx('guide-title')}>{item.title}</div>
          <div className={cx('guide-description')}>{item.description}</div>
          <AuthButton
            isAuth={permission?.[item.privilege] || false}
            tooltipType={TooltipType.Function}
            type="primary"
            disabled={item.disabled}
            onClick={() => createModal(item.key)}
          >
            {item.disabled ? '敬请期待' : '立即创建'}
          </AuthButton>
        </div>
      ))}
    </div>
  );
};

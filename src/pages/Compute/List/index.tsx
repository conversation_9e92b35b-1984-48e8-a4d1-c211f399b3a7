/**
 * 计算资源列表汇总页
 * 页面包括：
 * 1. 数据集成实例列表
 * 2. 数据处理实例
 * 3. 分析与AI搜索实例
 * 4. 资源池
 * <AUTHOR>
 */
import React, {useState, useMemo, useEffect} from 'react';
import _ from 'lodash';
import {Tabs} from 'acud';
import useUrlState from '@ahooksjs/use-url-state';
import DataProcessTab from './DataProcess';
import DataAnalysisTab from './DataAnalysis';
import ResourcePool from './ResourcePool';
import ConnAndInteg from './ConnAndInteg';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import useEnv from '@hooks/useEnv';

const cx = classNames.bind(styles);

const ComputeResourceList: React.FC = () => {
  const {isPrivate} = useEnv();
  const {TabPane} = Tabs;
  const [activeKey, setActiveKey] = useState('dataProcess');
  const [urlState, setUrlState] = useUrlState({tab: 'dataProcess'});

  const ComputeTabPanes = [
    {key: 'dataIntegration', tab: '源连接与集成实例', component: ConnAndInteg},
    {key: 'dataProcess', tab: '数据处理实例', component: DataProcessTab},
    !isPrivate && {key: 'dataAnalysis', tab: '分析与AI搜索实例', component: DataAnalysisTab},
    !isPrivate && {key: 'resourcePool', tab: '资源池', component: ResourcePool}
  ].filter(Boolean);

  const onTabsChange = (key: string) => {
    setActiveKey(key);
    setUrlState({tab: key});
  };
  const ActiveComponent = useMemo(() => {
    return _.find(ComputeTabPanes, {key: activeKey})?.component;
  }, [activeKey]);
  useEffect(() => {
    setActiveKey(urlState.tab || 'dataProcess');
  }, [urlState.tab]);
  return (
    <div className={cx('db-workspace-wrapper', 'list-wrapper')}>
      <div className={cx('compute-title', 'mb-[16px]')}>计算实例</div>
      <Tabs activeKey={activeKey} onChange={onTabsChange} className={cx('compute-tabs')}>
        {ComputeTabPanes.map((pane) => (
          <TabPane key={pane.key} tab={pane.tab}></TabPane>
        ))}
      </Tabs>
      {ActiveComponent ? <ActiveComponent /> : null}
    </div>
  );
};
export default ComputeResourceList;

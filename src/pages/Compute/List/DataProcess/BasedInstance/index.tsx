/**
 * 数据处理实例 - 常驻实例列表
 * 主要功能：
 * 1. 展示常驻实例列表，列表支持筛选、分页和刷新
 * 2. 提供新建常驻实例入口， 创建按钮根据用户权限确定是否展示
 * <AUTHOR>
 */
import {useState, useContext, useCallback, useMemo, forwardRef, useImperativeHandle} from 'react';
import {Table, Modal, toast, Button, Loading} from 'acud';
import {Plus1} from '@baidu/xicon-react-bigdata';
import urls from '@utils/urls';
import {getComputeResourceList, ComputeResourceItem, deleteComputeResource, Engine} from '@api/Compute';
import useTable from '@hooks/useTable';
import {useNavigate} from 'react-router-dom';
import {WorkspaceContext} from '@pages/index';
import {STATUS, PAY_TYPE} from '@pages/Compute/config';
import TextEllipsis from '@components/TextEllipsisTooltip';
import styles from '../index.module.less';
import classNames from 'classnames/bind';
import {useTableOperate} from '@pages/Compute/hooks/useTableOperate';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege, ResourceType} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {TooltipType} from '@components/AuthComponents/constants';
import {useRegion} from '@hooks/useRegion';
import useEnv from '@hooks/useEnv';

const cx = classNames.bind(styles);
const defaultEngine = [Engine.Ray, Engine.Spark];
interface BasedInstanceListProps {
  isCreateDisabled: boolean;
}
export interface RefreshableListRef {
  refresh: () => void;
}
const BasedInstanceList = (_, ref) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [engineFilter, setEngineFilter] = useState<string[]>(defaultEngine);
  const navigate = useNavigate();
  const authList = useWorkspaceAuth([Privilege.EtlComputeCreate]);
  const canCreate = authList[Privilege.EtlComputeCreate];
  const {isPrivate} = useEnv();
  const {currentRegion} = useRegion();
  // 融合态 - 成都region POC阶段集群线下创建集群，禁用创建按钮
  const isChengdu = !isPrivate && currentRegion.id === 'cd';

  const {
    loading,
    dataSource,
    tableProps,
    loadTableData: loadComputeList
  } = useTable<ComputeResourceItem>({
    getTableApi: getComputeResourceList,
    refreshDeps: [statusFilter, workspaceId, engineFilter],
    extraParams: {
      workspaceId,
      status: statusFilter?.join(',') || '',
      engine: engineFilter?.join(',') || defaultEngine.join(',')
    },
    listKey: 'computes',
    handleTableChange: (pagination, filters, sorter, extra) => {
      if (extra?.action === 'filter') {
        setStatusFilter(filters.status as string[]);
        setEngineFilter(filters.engine as string[]);
      }
    }
  });
  useImperativeHandle(ref, () => ({
    refresh: loadComputeList
  }));

  const {renderOperate, renderPermissionModal} = useTableOperate({
    workspaceId,
    resourceType: ResourceType.EtlCompute,
    onDelete(record: ComputeResourceItem) {
      Modal.confirm({
        title: '删除实例',
        content:
          '实例删除后不可恢复，关联任务的运行状态会受影响，关联任务需要重新指定计算实例。确定删除实例？',
        onOk: async () => {
          const res = await deleteComputeResource({
            workspaceId,
            computeId: record.computeId || ''
          });
          if (res.success) {
            toast.success({
              message: '删除成功',
              duration: 3
            });
            loadComputeList();
          }
        }
      });
    }
  });

  const columns = useMemo(
    () =>
      [
        {
          title: '实例 ID',
          dataIndex: 'computeId',
          key: 'computeId',
          width: 150
        },
        {
          title: '实例名称',
          dataIndex: 'name',
          key: 'name',
          width: 200,
          render: (name) => (
            <TextEllipsis tooltip={name} width={300}>
              {name}
            </TextEllipsis>
          )
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 100,
          filterMultiple: true,
          filters: STATUS.toArray(),
          filteredValue: statusFilter,
          render: (status: string) => {
            const statusObj = STATUS.fromValue(status);
            return <span className={cx('compute-status', statusObj.className)}>{statusObj.text}</span>;
          }
        },
        {
          title: '节点规格',
          dataIndex: 'clusterType',
          key: 'clusterType',
          width: 120,
          render: (_, record: ComputeResourceItem) => `${record.clusterType}（${record.nodeCount} 个节点）`
        },
        {
          title: '实例类型',
          dataIndex: 'engine',
          key: 'engine',
          filterMultiple: true,
          filters: [
            {text: Engine.Ray, value: Engine.Ray},
            {text: Engine.Spark, value: Engine.Spark}
          ],
          filterValue: engineFilter,
          width: 120
        },
        {
          title: '镜像版本',
          dataIndex: 'mirrorVersion',
          key: 'mirrorVersion',
          width: 120
        },
        {
          title: '创建时间',
          dataIndex: 'createdAt',
          key: 'createdAt',
          sorter: true,
          width: 180
        },
        isPrivate
          ? null
          : {
              title: '付费方式',
              dataIndex: 'chargeType',
              key: 'chargeType',
              width: 120,
              render: (value: string) => {
                return PAY_TYPE.getTextFromValue(value);
              }
            },
        {
          title: '操作',
          key: 'operation',
          width: 150,
          render: (record: ComputeResourceItem) => {
            return renderOperate(record);
          }
        }
      ].filter(Boolean),
    [renderOperate, statusFilter, isPrivate]
  );

  const showBlankSpace = useMemo(
    () => dataSource.length === 0 && !statusFilter?.length,
    [dataSource.length, statusFilter?.length]
  );
  // 创建实例
  const onCreateClick = useCallback(() => {
    navigate(`${urls.computeCreateBasedInstance}?workspaceId=${workspaceId}`);
  }, [navigate, workspaceId]);

  // 渲染空白状态
  const blankSpace = (
    <div className={cx('blank-space', 'h-full')}>
      <div className={cx('blank-title')}>创建常驻实例</div>
      <div className={cx('blank-desc')}>创建常驻实例用于运行工作流任务</div>
      <AuthComponents isAuth={canCreate} tooltipType={TooltipType.Function}>
        <Button
          className={cx('blank-btn')}
          type="primary"
          icon={<Plus1 className="w-4 h-4" />}
          disabled={isChengdu}
          onClick={onCreateClick}
        >
          立即创建
        </Button>
      </AuthComponents>
      <div className={cx('blank-img')}></div>
    </div>
  );

  if (loading && dataSource.length === 0 && !statusFilter?.length) {
    return <Loading loading={loading} />;
  }

  return (
    <>
      {showBlankSpace ? (
        blankSpace
      ) : (
        <>
          <Table className={cx('compute-table')} columns={columns} rowKey="computeId" {...tableProps} />
          {renderPermissionModal()}
        </>
      )}
    </>
  );
};
const RefBasedInstanceList = forwardRef<RefreshableListRef, BasedInstanceListProps>(BasedInstanceList);
export default RefBasedInstanceList;

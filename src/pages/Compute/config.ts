import {Engine} from '@api/Compute';
import {Privilege} from '@api/permission/type';
import Enum from '@helpers/enum';
import {RULE} from '@utils/regs';
export const NAME_LIMIT_LENGTH = 64;
export const NAME_REGEX = /^([a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5\-_/.]{0,63})?$/;
export const NAME_ERROR_MESSAGE =
  '由大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-64';

export const PAY_TYPE = new Enum(
  {
    alias: 'PREPAID',
    text: '包年包月',
    value: 'Prepaid',
    desc: '先付费后使用，价格更低廉',
    className: 'prepaid'
  },
  {
    alias: 'POSTPAID',
    text: '按量付费',
    value: 'Postpaid',
    desc: '先使用后付费，按需开通',
    className: 'postpaid'
  }
);

export const STATUS = new Enum(
  {
    alias: 'DEPLOY',
    text: '生效中',
    value: 'DEPLOY',
    className: 'deploying'
  },
  {
    alias: 'RUNNING',
    text: '运行中',
    value: 'RUNNING',
    className: 'running'
  },
  {
    alias: 'INVALID',
    text: '失效',
    value: 'INVALID',
    className: 'invalid'
  },
  {
    alias: 'CREATED_FAIL',
    text: '创建失败',
    value: 'CREATED_FAIL',
    className: 'created-fail'
  }
);
// 任务实例状态
export const TASK_STATUS = new Enum(
  {
    alias: 'DEPLOY',
    text: '生效中',
    value: 'DEPLOY',
    className: 'deploying'
  },
  {
    alias: 'RUNNING',
    text: '运行中',
    value: 'RUNNING',
    className: 'running'
  },
  // {
  //   alias: 'INVALID',
  //   text: '失效',
  //   value: 'INVALID',
  //   className: 'invalid'
  // },
  {
    alias: 'SUCCESS',
    text: '已停止',
    value: 'SUCCESS',
    className: 'created-fail'
  }
);

// 计算实例类型
export const ComputedMap = {
  [Engine.Ray]: {
    text: '常驻实例',
    key: Engine.Ray,
    engineText: '镜像版本',
    rule: NAME_REGEX,
    ruleText: NAME_ERROR_MESSAGE
  },
  [Engine.Doris]: {
    text: '查询检索实例',
    key: Engine.Doris,
    engineText: '实例版本',
    rule: RULE.specialNameStartEn64,
    ruleText: RULE.specialNameStartEn64Text
  },
  [Engine.Seatunnel]: {
    text: '源连接与集成实例',
    key: Engine.Seatunnel,
    engineText: '',
    rule: NAME_REGEX,
    ruleText: NAME_ERROR_MESSAGE
  }
};

export const ComputeTabPanes = Object.keys(ComputedMap).map((key) => ({
  key,
  tab: ComputedMap[key].text
}));

export const privilegeList = [
  {value: Privilege.Use, label: '使用'},
  {value: Privilege.Manage, label: '管理'}
];

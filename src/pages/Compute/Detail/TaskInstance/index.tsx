/**
 * 任务实例详情页
 *
 * <AUTHOR>
 */
import React, {useContext, useState, useMemo, useEffect, useCallback} from 'react';
import _ from 'lodash';
import {Tabs, Loading, Breadcrumb} from 'acud';
import {useRequest} from 'ahooks';
import useUrlState from '@ahooksjs/use-url-state';
import {useNavigate, Link} from 'react-router-dom';
import {useRegion} from '@hooks/useRegion';

import {WorkspaceContext} from '@pages/index';
import {
  TaskInstanceDetailResult,
  getTaskInstanceDetail,
  TASK_INSTANCE_STATUS,
  getVpcList,
  type Vpc,
  getSubnetList,
  type Subnet,
  getComputeResourcePoolList
} from '@api/Compute';
import DetailWrapper from '@pages/Compute/components/DetailWrapper';
import {PAY_TYPE} from '@pages/Compute/config';
import urls from '@utils/urls';
import useEnv from '@hooks/useEnv';
import classNames from 'classnames/bind';
import styles from './index.module.less';
const cx = classNames.bind(styles);

const TaskInstanceDetail: React.FC = () => {
  const {TabPane} = Tabs;
  const navigate = useNavigate();
  const {currentRegion} = useRegion();
  const {isPrivate} = useEnv();
  const [urlState] = useUrlState();
  const {workspaceId} = useContext(WorkspaceContext);
  const [detail, setDetail] = useState<TaskInstanceDetailResult | null>(null);
  const [vpcList, setVpcList] = React.useState<Vpc[]>([]);
  const {run: getVpcListRun} = useRequest(getVpcList, {
    manual: true,
    onSuccess: (res) => {
      if (!res.success) {
        return;
      }
      setVpcList(res.result);
    }
  });
  const [subnetList, setSubnetList] = React.useState<Subnet[]>([]);
  const [clusterTypeOptions, setClusterTypeOptions] = useState([]);
  const {run: getSubnetListRun} = useRequest(getSubnetList, {
    manual: true,
    onSuccess: (res) => {
      setSubnetList(res.page.result);
    }
  });
  const {run: getPoolList} = useRequest(getComputeResourcePoolList, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        const poolList = res.result.pools;
        setClusterTypeOptions(
          _.map(poolList, (item) => ({
            label: item.name,
            value: item.resourcePoolId,
            availableZone: item.availableZone,
            subnetId: item.subnetId,
            // 集群资源规格
            clusterType: item.clusterType
          }))
        );
      }
    }
  });
  useEffect(() => {
    getPoolList({workspaceId});
  }, [workspaceId, getPoolList]);
  const {loading, run: getInstanceDetail} = useRequest(getTaskInstanceDetail, {
    manual: true,
    onSuccess: (res) => {
      if (!res.success) {
        return;
      }
      setDetail(res.result);
    }
  });
  useEffect(() => {
    getInstanceDetail({
      workspaceId,
      batchJobId: urlState.instanceId
    });
  }, [workspaceId, urlState.instanceId, getInstanceDetail]);
  useEffect(() => {
    if (isPrivate) {
      return;
    }
    getVpcListRun();
    getSubnetListRun({vpcId: detail?.vpcId});
  }, [getVpcListRun, getSubnetListRun, detail?.vpcId, isPrivate]);
  // 渲染vpc名称
  const renderVpcName = useCallback(() => {
    if (!detail?.vpcId) {
      return;
    }
    const vpc = vpcList.find((item) => item.vpcId === detail.vpcId);
    return vpc ? `${vpc.name}（${vpc.cidr}）` : detail.vpcId;
  }, [vpcList, detail]);
  // 渲染可用区-子网
  const renderSubnetName = useCallback(() => {
    if (!detail?.subnetId) {
      return;
    }
    const subnet = subnetList.find((item) => item.subnetId === detail.subnetId);
    const zoneName = detail.availableZone.replace('zone', '');
    return subnet ? `${zoneName}-${subnet.name}` : `${zoneName}-${detail.subnetId}`;
  }, [detail, subnetList]);
  // 详情页展示内容汇总
  const detailItems = useMemo(() => {
    return [
      !isPrivate
        ? {
            title: '付费及地域',
            details: [
              {
                label: '付费方式',
                value: detail?.chargeType ? PAY_TYPE.getTextFromValue(detail?.chargeType) : '-'
              },
              {label: '地域', value: currentRegion?.label || '-'}
            ]
          }
        : null,
      !isPrivate
        ? {
            title: '网络及可用区',
            details: [
              {label: '所有网络', value: renderVpcName() || '-'},
              {label: '可用区与子网', value: renderSubnetName() || '-'}
            ]
          }
        : null,
      {
        title: '实例信息',
        details: [
          {label: '实例名称', value: detail?.name || '-'},
          {label: '实例类型', value: detail?.engine || '-'},
          {label: '创建时间', value: detail?.createdAt || '-'},
          {label: '镜像版本', value: detail?.mirrorVersion || '-'},
          {
            label: '关联工作流',
            value: detail?.workflowId ? (
              <Link to={`${urls.jobDetail}?workspaceId=${workspaceId}&jobId=${detail?.workflowId}`}>
                {detail.workflowName}（{detail.workflowId}）
              </Link>
            ) : (
              '-'
            )
          },
          {label: '关联任务', value: detail?.taskName || '-'}
        ]
      },
      {
        title: '节点信息',
        details: [
          {label: '节点类型', value: detail?.processor || '-'},
          {label: '节点规格', value: detail?.clusterType || '-'},
          {label: '节点数量', value: detail?.nodeCnt || '-'}
        ]
      }
    ].filter(Boolean);
  }, [detail, currentRegion?.label, renderSubnetName, renderVpcName, workspaceId, isPrivate]);
  return (
    <div className={cx('db-workspace-wrapper', 'instance-detail-wrapper')}>
      {loading ? (
        <Loading loading />
      ) : (
        <div className={cx('instance-detail-content')}>
          <Breadcrumb className={cx('breadcrumb', 'mb-[12px]')}>
            <Breadcrumb.Item>
              <Link to={`${urls.compute}?workspaceId=${workspaceId}&tab=dataProcess`}>计算实例</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <Link to={`${urls.compute}?workspaceId=${workspaceId}&tab=dataProcess&secondTab=Task`}>
                数据处理实例
              </Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>任务实例</Breadcrumb.Item>
          </Breadcrumb>
          <div className={cx('title', 'mb-[8px]')}>
            <div className={cx('title-text')}>{detail?.name}</div>
          </div>
          <Tabs defaultActiveKey="detail">
            <TabPane tab="详情" key="detail">
              {detailItems.map(({title, details}) => (
                <DetailWrapper key={title} title={title} details={details} />
              ))}
            </TabPane>
          </Tabs>
        </div>
      )}
    </div>
  );
};
export default TaskInstanceDetail;

import React, {useContext, useEffect, useState} from 'react';
import {useNavigate, Link} from 'react-router-dom';
import {Tabs, Loading, Button, Breadcrumb} from 'acud';
import {useRequest} from 'ahooks';
import useUrlState from '@ahooksjs/use-url-state';
import {getComputeResourcePoolDetail, GetComputeResourcePoolDetailResult} from '@api/Compute';
import {WorkspaceContext} from '@pages/index';
import OverviewTab from './OverviewTab';
import DetailTab from './DetailTab';
import {deleteResourcePool} from '@pages/Compute/List/ResourcePool';
import urls from '@utils/urls';

import classNames from 'classnames/bind';
import styles from './index.module.less';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {TooltipType} from '@components/AuthComponents/constants';
import {Privilege} from '@api/permission/type';
const cx = classNames.bind(styles);

const ResourcePoolDetail: React.FC = () => {
  const {TabPane} = Tabs;
  const {workspaceId} = useContext(WorkspaceContext);
  const navigate = useNavigate();
  const [urlState, setUrlState] = useUrlState();
  const [detail, setDetail] = useState<GetComputeResourcePoolDetailResult | null>(null);

  const {run: getDetail, loading} = useRequest(getComputeResourcePoolDetail, {
    manual: true,
    onSuccess: (res) => {
      if (!res.success) {
        return;
      }
      setDetail(res.result);
    }
  });

  useEffect(() => {
    getDetail({
      workspaceId,
      resourcePoolId: urlState.resourcePoolId
    });
  }, [workspaceId, urlState.resourcePoolId, getDetail]);

  const [activeKey, setActiveKey] = useState(urlState.tab || 'overview');
  useEffect(() => {
    setActiveKey(urlState.tab || 'overview');
  }, [urlState.tab]);
  const onTabChange = (key: string) => {
    setActiveKey(key);
    setUrlState({tab: key});
  };

  const handleDeleteClick = () => {
    deleteResourcePool({
      workspaceId,
      resourcePoolId: urlState.resourcePoolId,
      cb: () => {
        navigate(`${urls.compute}?workspaceId=${workspaceId}&tab=resourcePool`);
      }
    });
  };

  const canManage = detail?.privileges?.includes(Privilege.Manage);

  return (
    <div className={cx('db-workspace-wrapper', 'pool-detail-wrapper')}>
      {loading ? (
        <Loading loading />
      ) : (
        <>
          <Breadcrumb className={cx('breadcrumb')}>
            <Breadcrumb.Item>
              <Link to={`${urls.compute}?workspaceId=${workspaceId}&tab=dataProcess`}>计算实例</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <Link to={`${urls.compute}?workspaceId=${workspaceId}&tab=resourcePool`}>资源池</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>资源池详情</Breadcrumb.Item>
          </Breadcrumb>
          <div className={cx('title')}>
            <div className={cx('title-text')}>{detail?.name}</div>
            <AuthComponents isAuth={canManage} tooltipType={TooltipType.Resource}>
              <Button onClick={handleDeleteClick}>删除</Button>
            </AuthComponents>
          </div>
          <Tabs activeKey={activeKey} onChange={onTabChange}>
            <TabPane tab="概览" key="overview">
              {detail && <OverviewTab detail={detail} />}
            </TabPane>
            <TabPane tab="详情" key="detail">
              {detail && <DetailTab detail={detail} />}
            </TabPane>
          </Tabs>
        </>
      )}
    </div>
  );
};

export default ResourcePoolDetail;

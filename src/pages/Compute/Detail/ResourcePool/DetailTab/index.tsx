import React, {useEffect, useMemo, useCallback} from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {
  GetComputeResourcePoolDetailResult,
  getVpcList,
  type Vpc,
  getSubnetList,
  type Subnet
} from '@api/Compute';
import {PAY_TYPE} from '@pages/Compute/config';
import {useRegion} from '@hooks/useRegion';
import DetailWrapper from '@pages/Compute/components/DetailWrapper';
import {useRequest} from 'ahooks';
import useEnv from '@hooks/useEnv';
const cx = classNames.bind(styles);

const DetailTab: React.FC<{detail: GetComputeResourcePoolDetailResult}> = ({detail}) => {
  const {currentRegion} = useRegion();
  const {isPrivate} = useEnv();
  const [vpcList, setVpcList] = React.useState<Vpc[]>([]);
  const [vpc, setVpc] = React.useState<Vpc | null>(null);

  const {run: getVpcListRun} = useRequest(getVpcList, {
    manual: true,
    onSuccess: (res) => {
      if (!res.success) {
        return;
      }
      setVpcList(res.result);
    }
  });

  // 渲染vpc名称
  const renderVpcName = useCallback(() => {
    if (!detail?.vpcId) {
      return;
    }
    const vpc = vpcList.find((item) => item.shortId === detail.vpcId);
    if (vpc) {
      setVpc(vpc);
    }
    return vpc ? `${vpc.name}（${vpc.cidr}）` : detail.vpcId;
  }, [vpcList, detail]);

  const [subnetList, setSubnetList] = React.useState<Subnet[]>([]);
  const {run: getSubnetListRun} = useRequest(getSubnetList, {
    manual: true,
    onSuccess: (res) => {
      setSubnetList(res.page.result);
    }
  });

  // 渲染可用区-子网
  const renderSubnetName = useCallback(() => {
    if (!detail?.subnetId) {
      return;
    }
    const subnet = subnetList.find((item) => item.shortId === detail.subnetId);
    const zoneName = detail.availableZone.replace('zone', '');
    return subnet ? `${zoneName}-${subnet.name}` : `${zoneName}-${detail.subnetId}`;
  }, [detail, subnetList]);

  useEffect(() => {
    if (isPrivate) {
      return;
    }
    getVpcListRun();
  }, [getVpcListRun, isPrivate]);

  useEffect(() => {
    if (!vpc || isPrivate) {
      return;
    }
    getSubnetListRun({vpcId: vpc.vpcId});
  }, [getSubnetListRun, vpc, isPrivate]);

  const detailItems = useMemo(() => {
    return [
      !isPrivate
        ? {
            title: '付费及地域',
            details: [
              {
                label: '付费方式',
                value: detail?.chargeType ? PAY_TYPE.getTextFromValue(detail.chargeType) : '-'
              },
              {label: '地域', value: currentRegion?.label}
            ]
          }
        : null,
      !isPrivate
        ? {
            title: '网络及可用区',
            details: [
              {label: '所有网络', value: renderVpcName() || '-'},
              {label: '可用区与子网', value: renderSubnetName() || '-'}
            ]
          }
        : null,
      {
        title: '资源池信息',
        details: [
          {label: '资源池名称', value: detail?.name || '-'},
          {label: '资源规格', value: detail?.clusterType || '-'},
          {label: '资源数量', value: `${detail?.nodeCount}个`},
          {label: '镜像版本', value: detail?.mirrorVersion || '-'}
        ]
      }
    ].filter(Boolean);
  }, [detail, currentRegion.label, renderVpcName, renderSubnetName, isPrivate]);
  return (
    <>
      {detailItems.map(({title, details}) => (
        <DetailWrapper key={title} title={title} details={details} />
      ))}
    </>
  );
};

export default DetailTab;

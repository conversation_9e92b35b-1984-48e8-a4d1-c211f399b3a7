import {useRef} from 'react';
import {STATUS, PAY_TYPE} from '../config';
import {Space, Link} from 'acud';
import {useRegion} from '@hooks/useRegion';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {TooltipType} from '@components/AuthComponents/constants';
import PermissionModal from '@components/Workspace/PermissionModal';
import {PermissionModalRef} from '@components/Workspace/PermissionModal';
import {ResourceType} from '@api/permission/type';
import {privilegeList} from '@pages/Compute/config';

interface UseTableOperateProps {
  workspaceId: string;
  onDelete: (record: any) => void;
  resourceType: ResourceType;
}
/**
 * 实例表格操作hook
 */
export const useTableOperate = ({workspaceId, onDelete, resourceType}: UseTableOperateProps) => {
  const permissionModalRef = useRef<PermissionModalRef>(null);
  // const {currentRegion} = useRegion();

  function isDeleteDisabled(record) {
    // 预付费除失效状态外不能删除
    if (record.chargeType === PAY_TYPE.PREPAID) {
      return ![STATUS.INVALID].includes(record.status);
    }
    return [STATUS.DEPLOY].includes(record.status);
  }

  function isRenewDisabled(record) {
    // 后付费不能续费
    if (record.chargeType === PAY_TYPE.POSTPAID) {
      return true;
    }
    return [STATUS.DEPLOY, STATUS.CREATED_FAIL].includes(record.status);
  }

  function renderOperate(record) {
    // const renewLink = `/billing/renew?serviceType=EDAP&region=${currentRegion.id}&instanceIds=${record.computeId}&confirmV2Url=%2Fapi%2Fbes%2Frenew%2Fconfirm`;
    const {privileges = []} = record;
    const canManage = privileges.includes(Privilege.Manage);
    return (
      <Space>
        {/* <Link href={renewLink} disabled={isRenewDisabled(record)}>
          续费
        </Link> */}
        <AuthComponents isAuth={canManage} tooltipType={TooltipType.Resource}>
          <Link
            disabled={isDeleteDisabled(record)}
            onClick={() => {
              onDelete(record);
            }}
          >
            删除
          </Link>
        </AuthComponents>
        <AuthComponents isAuth={canManage} tooltipType={TooltipType.Resource}>
          <Link
            onClick={() => {
              permissionModalRef.current?.open({
                resourceType,
                resourceId: record.computeId,
                resourceName: record.name,
                privilegeList
              });
            }}
          >
            权限管理
          </Link>
        </AuthComponents>
      </Space>
    );
  }

  function renderPermissionModal() {
    return <PermissionModal workspaceId={workspaceId} ref={permissionModalRef} />;
  }

  return {
    renderOperate,
    renderPermissionModal,
    isDeleteDisabled,
    isRenewDisabled
  };
};

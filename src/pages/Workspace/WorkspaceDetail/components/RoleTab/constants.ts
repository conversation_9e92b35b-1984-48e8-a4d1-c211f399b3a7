import {Privilege, RoleType} from '@api/permission/type';

export const RoleMap = {
  [RoleType.System]: '系统角色',
  [RoleType.User]: '自定义角色'
};

export enum EditType {
  Edit = 'edit',
  Create = 'create',
  View = 'view'
}

export const DrawerTitle = {
  [EditType.Edit]: '编辑角色',
  [EditType.Create]: '创建角色',
  [EditType.View]: '查看角色'
};

export const orderMap = {
  ascend: 'asc',
  descend: 'desc'
};

export const ManagerName = '空间管理员';

// 功能权限手风琴配置配置
export const PrivilegeCollapseConfig = [
  {
    // 一级功能
    title: '工作区',
    // 菜单权限
    menuPrivilege: Privilege.WorkspaceMenu
    // children: [
    //   {
    //     // 二级功能
    //     groupName: '文件夹',
    //     // 功能点
    //     privilege: [
    //       {
    //         name: '新建文件夹',
    //         value: Privilege.DirCreate
    //       }
    //     ]
    //   },
    //   {
    //     groupName: '文件',
    //     privilege: [
    //       {
    //         name: '导入文件',
    //         value: Privilege.FileImport
    //       }
    //     ]
    //   },
    //   {
    //     groupName: 'Notebook',
    //     privilege: [
    //       {
    //         name: '新建 Notebook',
    //         value: Privilege.NotebookCreate
    //       }
    //     ]
    //   }
    // ]
  },
  {
    title: '元数据',
    menuPrivilege: Privilege.CatalogMenu
  },
  {
    title: '计算实例',
    menuPrivilege: Privilege.ComputeMenu,
    children: [
      {
        groupName: '源连接与集成',
        privilege: [{name: '新建', value: Privilege.IntegrationComputeCreate}]
      },
      {
        groupName: '数据处理',
        privilege: [
          {name: '新建常驻实例', value: Privilege.EtlComputeCreate},
          {name: '新建任务实例模版', value: Privilege.EtlJobTemplateCreate}
        ]
      },
      {
        groupName: '分析与AI搜索',
        privilege: [{name: '新建', value: Privilege.AnalysisComputeCreate}]
      },
      {
        groupName: '资源池',
        privilege: [{name: '新建', value: Privilege.ResourcePoolCreate}]
      }
    ]
  },
  {
    title: '数据集成',
    menuPrivilege: Privilege.IntegrationMenu,
    children: [
      {
        groupName: '文件采集',
        privilege: [
          {name: '新建', value: Privilege.UnstructuredIntegrationCreate}
          // {name: '批量停止', value: Privilege.UnstructuredIntegrationStop},
          // {name: '批量删除', value: Privilege.UnstructuredIntegrationDelete},
          // {name: '批量运行', value: Privilege.UnstructuredIntegrationExecute}
        ]
      },
      {
        groupName: '离线同步',
        privilege: [
          {name: '新建', value: Privilege.StructuredIntegrationCreate}
          // {name: '批量运行', value: Privilege.StructuredIntegrationExecute},
          // {name: '批量发布', value: Privilege.StructuredIntegrationPublish},
          // {name: '批量删除', value: Privilege.StructuredIntegrationDelete},
          // {name: '批量编辑', value: Privilege.StructuredIntegrationModify}
        ]
      }
    ]
  },
  {
    title: '工作流',
    menuPrivilege: Privilege.WorkflowMenu,
    children: [
      {
        groupName: '按钮',
        privilege: [
          {name: '新建工作流', value: Privilege.WorkflowCreate},
          {name: '导入工作流', value: Privilege.WorkflowImport}
        ]
      }
    ]
  },
  {
    title: '运行记录',
    menuPrivilege: Privilege.WorkflowInstanceMenu
  }
];

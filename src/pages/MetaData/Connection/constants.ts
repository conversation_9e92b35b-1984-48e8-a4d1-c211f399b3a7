import {FileSourceType} from '@/api/integration/type';

/** 数据源 - NoSQL类型 */
export enum ENoSQLConnectionType {
  MongoDB = 'MongoDB',
  Redis = 'Redis'
}

/** 数据源 - 关系型数据库类型 */
export enum ERelationalConnectionType {
  MySQL = 'MySQL',
  Oracle = 'Oracle',
  SQLServer = 'SQLServer',
  PostgreSQL = 'PostgreSQL'
}

/** 数据源类型 */
export type TConnectionType = FileSourceType | ENoSQLConnectionType;

/** 数据源类型映射 */
export const ConnectionTypeMap = [
  {
    groupName: '关系型数据库',
    typeList: [
      {
        type: ERelationalConnectionType.MySQL,
        icon: 'mysql',
        color: '#0E5A87'
      },
      {
        type: ERelationalConnectionType.Oracle,
        icon: 'oracle',
        color: '#ED1C24'
      },
      {
        type: ERelationalConnectionType.SQLServer,
        icon: 'sqlserver',
        color: '#FFFFFF'
      },
      {
        type: ERelationalConnectionType.PostgreSQL,
        icon: 'postgresql',
        color: '#FFFFFF'
      }
    ]
  },
  {
    groupName: '半结构化存储',
    typeList: [
      {
        type: FileSourceType.SFTP,
        icon: 'sftp',
        color: '#F9B10B'
      },
      {
        type: FileSourceType.FTP,
        icon: 'ftp',
        color: '#00B1E3'
      },
      {
        type: FileSourceType.HDFS,
        icon: 'hdfs',
        color: '#fff'
      }
    ]
  }
  // {
  //   groupName: 'NoSQL',
  //   typeList: [
  //     {
  //       type: ENoSQLConnectionType.MongoDB,
  //       icon: 'mongodb',
  //       color: '#001E2B'
  //     },
  //     {
  //       type: ENoSQLConnectionType.Redis,
  //       icon: 'redis',
  //       color: '#FFFFFF'
  //     }
  //   ]
  // }
];

/** 数据源全类型映射 */
export const ConnectionTypeFlatMap = ConnectionTypeMap.reduce(
  (acc, curr) => ({
    ...acc,
    ...Object.fromEntries(curr.typeList.map((item) => [item.type, {...item, groupName: curr.groupName}]))
  }),
  {} as Record<TConnectionType, any>
);
// 认证方式。HDFS 必传
export enum AuthenticationTypeEnum {
  None = 'None',
  Kerberos = 'Kerberos'
}
export const AuthenticationTypeMap = {
  [AuthenticationTypeEnum.None]: '无',
  [AuthenticationTypeEnum.Kerberos]: 'Kerberos认证'
};

/**
 * @file 数据源详情
 * <AUTHOR>
 */

import {FC, useCallback, useEffect, useState, useContext, useMemo} from 'react';
import useUrlState from '@ahooksjs/use-url-state';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {useSearchParams, useNavigate} from 'react-router-dom';
import {Breadcrumb, Tabs, Button, Space, Loading, Modal, toast, Dropdown, Menu} from 'acud';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import urls from '@/utils/urls';
import {WorkspaceContext} from '@pages/index';
import {useRequest} from 'ahooks';
import {queryConnectionDetail, IConnection, deleteConnection} from '@api/connection';
import DetailTab from './components/DetailTab';
import EditModal from '../components/EditModal';
import IconSvg from '@components/IconSvg';
import styles from './index.module.less';
import {ResourceType, Privilege} from '@api/permission/type';
import PermissionManage from '@components/PermissionManage';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import AuthButton from '@components/AuthComponents/AuthButton';
import {TooltipType} from '@components/AuthComponents/constants';
import {ERelationalConnectionType} from '../constants';

const PREFIX = 'connection-detail';

const ConnectionDetail: FC = () => {
  const {workspaceId} = useContext(WorkspaceContext);
  const permission = useWorkspaceAuth([
    Privilege.UnstructuredIntegrationCreate,
    Privilege.StructuredIntegrationCreate
  ]);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const name = searchParams.get('name');

  const [connectionDetail, setConnectionDetail] = useState<IConnection>();
  const [activeKey, setActiveKey] = useState();
  const [isModalVisible, setIsModalVisible] = useState(false); // 编辑弹窗是否可见
  const [authList, setAuthList] = useState<string[]>([]); // 权限点列表

  const canManage = useMemo(() => {
    return authList.includes(Privilege.Manage);
  }, [authList]);

  // 获取数据源详情
  const getConnectionDetail = useCallback(() => {
    !!name && runConnectionDetail(workspaceId, name);
  }, [name, workspaceId]);

  // 进页面时，先获取数据源详情
  useEffect(() => {
    getConnectionDetail();
  }, [getConnectionDetail]);

  const {run: runConnectionDetail, loading: queryConnectionDetailLoading} = useRequest(
    queryConnectionDetail,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.success) {
          setConnectionDetail(res?.result);
          setAuthList(res?.result?.privileges || []);
        }
      }
    }
  );

  // 删除数据源
  const runDeleteConnection = useCallback(
    (params) => {
      return deleteConnection(workspaceId, params);
    },
    [workspaceId]
  );

  // tab切换
  const handleTabChange = useCallback((activeKey) => {
    setActiveKey(activeKey);
  }, []);

  // 删除数据源
  const handleDelete = useCallback(() => {
    Modal.confirm({
      title: '删除数据源',
      content: `删除后⽆法恢复！请确定是否要删除“${connectionDetail.name}”`,
      okText: '删除',
      onOk() {
        return runDeleteConnection([connectionDetail?.name]).then((res) => {
          if (res.success) {
            toast.success({message: '删除成功', duration: 5});
            navigate(urls.connection);
          }
        });
      },
      onCancel() {}
    });
  }, [connectionDetail, runDeleteConnection]);

  const onClickMenuItem = (e: any) => {
    if (!canManage) {
      return;
    }
    switch (e.key) {
      case 'edit':
        setIsModalVisible(true);
        break;
      case 'delete':
        handleDelete();
        break;
      default:
        break;
    }
  };

  // 关闭弹窗
  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  // 是否为新建库表离线采集
  const isStructuredIntegrationCreate = useMemo(() => {
    return [ERelationalConnectionType.MySQL].includes(connectionDetail?.type as any);
  }, [connectionDetail?.type]);

  // 新建按钮
  const onCreate = useCallback(() => {
    window.open(
      `${window.location.pathname}#${isStructuredIntegrationCreate ? urls.offlineCollectConfig : urls.fileCollectCreate}?sourceType=${connectionDetail?.type}&sourceConnectionId=${connectionDetail?.name}&workspaceId=${workspaceId}`,
      '_blank'
    );
  }, [isStructuredIntegrationCreate, connectionDetail?.type, connectionDetail?.name, workspaceId]);

  return (
    <div className={styles[`${PREFIX}-container`]}>
      <Loading loading={queryConnectionDetailLoading} />
      <Breadcrumb>
        <Breadcrumb.Item>目录列表</Breadcrumb.Item>
        <Breadcrumb.Item>
          <a href={`${window.location.pathname}#${urls.connection}`}>数据源</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <div className={styles[`${PREFIX}-container-breadcrumb-name`]}>
            <Ellipsis tooltip={connectionDetail?.name}>{connectionDetail?.name}</Ellipsis>
          </div>
        </Breadcrumb.Item>
      </Breadcrumb>
      <div className={styles[`${PREFIX}-header`]}>
        <div className={styles[`${PREFIX}-title`]}>
          <Ellipsis tooltip={connectionDetail?.name}>{connectionDetail?.name}</Ellipsis>
        </div>
        <Space>
          <Dropdown
            overlay={
              <Menu onClick={onClickMenuItem}>
                <AuthMenuItem key="edit" isAuth={canManage}>
                  编辑
                </AuthMenuItem>
                <AuthMenuItem key="delete" isAuth={canManage}>
                  删除
                </AuthMenuItem>
              </Menu>
            }
          >
            <Button style={{width: 32, padding: 0}}>
              <IconSvg type="more" size={16} />
            </Button>
          </Dropdown>
          <AuthButton
            tooltipType={TooltipType.Function}
            isAuth={
              permission[
                isStructuredIntegrationCreate
                  ? Privilege.StructuredIntegrationCreate
                  : Privilege.UnstructuredIntegrationCreate
              ] && authList.includes(Privilege.UseConnection)
            }
            type="primary"
            onClick={onCreate}
            className="mr-[10px]"
          >
            {isStructuredIntegrationCreate ? '创建库表离线采集' : '创建文件离线采集'}
          </AuthButton>
        </Space>
      </div>

      <Tabs onChange={handleTabChange} activeKey={activeKey} className={styles[`${PREFIX}-tabs`]}>
        <Tabs.TabPane tab="详情" key="detail">
          <DetailTab connectionDetail={connectionDetail} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="权限管理" key="permission" disabled={!canManage}>
          <PermissionManage
            resourceType={ResourceType.Connection}
            resourceId={connectionDetail?.name}
            hasInheritedFrom={false}
            name={connectionDetail?.name}
            onSuccess={getConnectionDetail}
            manageDescription="授予对象类似所有者的权限，例如权限管理、删除"
          />
        </Tabs.TabPane>
      </Tabs>

      <EditModal
        connection={connectionDetail}
        isModalVisible={isModalVisible}
        handleCloseModal={handleCloseModal}
        successCallback={getConnectionDetail}
      />
    </div>
  );
};

export default ConnectionDetail;

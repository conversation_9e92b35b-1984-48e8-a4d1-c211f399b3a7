.connection-detail-container {
  width: 100%;
  border: 1px solid rgba(212, 214, 217, 0.6);
  border-radius: 6px;
  margin: 0 8px 8px 0;
  font-family: PingFang SC;
  background-color: #fff;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .connection-detail-header {
    margin: 16px 0 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .connection-detail-title {
      font-weight: 500;
      font-size: 22px;
      line-height: 32px;
      height: 32px;
      flex: 1;
      min-width: 0;
      margin-right: 64px;
    }
  }

  .connection-detail-container-breadcrumb-name {
    max-width: 180px;
    display: inline-flex;
    align-items: center;
  }

  .connection-detail-tabs {
    flex: 1;
    min-height: 600px;

    :global {
      .acud-tabs-nav {
        margin-bottom: 20px;
      }
      .acud-tabs-content {
        height: 100%;
      }
    }
  }
}

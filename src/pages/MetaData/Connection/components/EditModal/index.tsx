/**
 * @file 新建、编辑数据源弹窗
 * <AUTHOR>
 */

import {
  IConnection,
  IQueryConnectionListParams,
  createConnection,
  updateConnection,
  getConnectionVersion
} from '@api/connection';
import {FileSourceType} from '@api/integration/type';
import FilePathSelectWorkarea, {FileNodeTypeEnum} from '@components/FilePathSelectWorkarea';
import {WorkspaceContext} from '@pages/index';
import {RULE} from '@utils/regs';
import {Button, Form, Input, Modal, Radio, Steps, toast} from 'acud';
import cx from 'classnames';
import {FC, useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {
  AuthenticationTypeEnum,
  AuthenticationTypeMap,
  ConnectionTypeMap,
  ERelationalConnectionType,
  TConnectionType
} from '../../constants';
import ConnectionTest from '../ConnectionTest';
import TypeCard from '../TypeCard';
import styles from './index.module.less';

const {Step} = Steps;

interface IEditModalProps {
  /** 编辑的数据源 */
  connection?: IConnection;
  /** 弹窗是否打开 */
  isModalVisible: boolean;
  /** 关闭弹窗 */
  handleCloseModal: () => void;
  /** 提交成功后的回调 */
  successCallback?: (params?: IQueryConnectionListParams) => void;
}

const EditModal: FC<IEditModalProps> = ({connection, isModalVisible, handleCloseModal, successCallback}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [currentSetp, setCurrentSetp] = useState(0);
  const [selectedType, setSelectedType] = useState<TConnectionType>();
  const [connectionVersion, setConnectionVersion] = useState<string[]>([]);

  // 初始化
  useEffect(() => {
    // 打开弹窗
    if (isModalVisible) {
      if (connection) {
        // 编辑
        form.setFieldsValue(connection);
        setSelectedType(connection.type);
        setCurrentSetp(1);
      } else {
        // 新建
        // const firstType = ConnectionTypeMap[0].typeList[0].type;
        // form.setFieldsValue({
        //   type: firstType
        // });
        // setSelectedType(firstType);
      }
    }
  }, [isModalVisible, connection]);

  // 获取对应数据源版本
  useEffect(() => {
    if (selectedType) {
      getConnectionVersion().then((res: any) => {
        const allVersions = res?.result?.versions || [];
        setConnectionVersion(allVersions[selectedType]);
      });
    }
  }, [selectedType]);

  // 关闭弹窗
  const onCloseModal = useCallback(() => {
    handleCloseModal();
    form.resetFields();
  }, [form, handleCloseModal]);

  // 切换步骤
  const handleNextStep = useCallback(() => {
    if (currentSetp === 0) {
      const type = form.getFieldValue('type');
      if (type) {
        setCurrentSetp(1);
      }
    } else {
      setCurrentSetp(0);
    }
  }, [currentSetp, form]);

  // 提交表单
  const handleConfirm = useCallback(() => {
    // 表单校验
    form
      .validateFields()
      .then((values) => {
        setLoading(true);
        const request = connection ? updateConnection : createConnection;
        request(workspaceId, values)
          .then((res) => {
            if (res?.success) {
              toast.success({
                message: connection ? '编辑成功' : '创建成功',
                duration: 5
              });

              onCloseModal();
              successCallback({
                pageNo: 1
              });
            }
          })
          .finally(() => {
            setLoading(false);
          });
      })
      // 捕获校验错误
      .catch((err) => {
        scrollToError(err.errorFields);
      });
  }, [form, onCloseModal, successCallback]);

  const handleSelectType = useCallback(
    (type) => {
      setSelectedType(type);
      form.setFieldsValue({
        type
      });
    },
    [form]
  );

  // 滚动到错误字段
  const scrollToError = useCallback(
    (errorFields) => {
      setTimeout(() => {
        const fieldName = errorFields?.[0]?.name?.[0];
        fieldName &&
          form.scrollToField(fieldName, {
            behavior: 'smooth',
            block: 'center'
          });
      }, 100);
    },
    [form]
  );

  const dataSourceTitle = useMemo(() => {
    return selectedType ? `${selectedType}数据源` : '数据源';
  }, [selectedType]);

  return (
    <Modal
      closable={true}
      title={connection ? `编辑${dataSourceTitle}` : `创建${dataSourceTitle}`}
      width={currentSetp === 0 ? 772 : 772}
      bodyStyle={{minHeight: 0}}
      visible={isModalVisible}
      onCancel={onCloseModal}
      destroyOnClose={true}
      afterClose={() => {
        form.resetFields();
        setCurrentSetp(0);
        setSelectedType(undefined);
      }}
      className={cx(styles['connection-modal'], {[styles['connection-modal-step-1']]: currentSetp === 0})}
      footer={
        <div>
          <Button onClick={onCloseModal}>取消</Button>
          {!connection && (
            <Button disabled={!selectedType || loading} onClick={handleNextStep}>
              {currentSetp === 0 ? '下一步' : '上一步'}
            </Button>
          )}
          {currentSetp === 1 && (
            <Button type="primary" onClick={handleConfirm} loading={loading}>
              确定
            </Button>
          )}
        </div>
      }
    >
      <div className={styles['connection-modal-content']}>
        <Steps current={currentSetp} className={cx(styles['connection-steps'])}>
          <Step title="选择类型" />
          <Step title="建立连接" />
        </Steps>
        <div className={styles['connection-steps-content']}>
          {currentSetp !== 0 && <div className={styles['connection-steps-title']}>连接信息</div>}
          <Form labelAlign="left" layout="vertical" colon={false} labelWidth={80} form={form}>
            <div className={cx(styles['step-content'], {[styles['step-hidden']]: currentSetp !== 0})}>
              {ConnectionTypeMap.map((group, key) => {
                return (
                  <div className={styles['connection-type-group']} key={group.groupName}>
                    <div className={styles['connection-type-group-title']}>{group.groupName}</div>
                    <div className={styles['connection-type-group-list']}>
                      {group.typeList.map((item) => {
                        return (
                          <TypeCard
                            key={item.type}
                            type={item.type}
                            icon={item.icon}
                            color={item.color}
                            callback={handleSelectType}
                            isActive={item.type === selectedType}
                          />
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
            <Form.Item name="type" required hidden>
              <Input />
            </Form.Item>
            <div className={cx(styles['step-content'], {[styles['step-hidden']]: currentSetp !== 1})}>
              <Form.Item
                name="name"
                label="名称"
                rules={[
                  {required: true, message: '请输入名称'},
                  {pattern: RULE.connectionName, message: RULE.connectionNameText}
                ]}
              >
                <Input
                  placeholder="请输入名称"
                  allowClear
                  limitLength={32}
                  disabled={!!connection}
                  autoComplete="off"
                />
              </Form.Item>
              <Form.Item
                label="描述"
                name="comment"
                rules={[
                  {
                    validator: (_, value) =>
                      value?.length > 1024
                        ? Promise.reject(new Error('最大长度不可超过1024个字符'))
                        : Promise.resolve()
                  }
                ]}
              >
                <Input.TextArea
                  placeholder="请输入描述"
                  allowClear
                  limitLength={1024}
                  style={{height: 80, maxHeight: 300, resize: 'vertical'}}
                />
              </Form.Item>
              {selectedType !== FileSourceType.HDFS ? (
                <>
                  <Form.Item
                    name="host"
                    label="主机"
                    rules={[
                      {required: true, message: '请输入主机'},
                      {
                        validator: (_, value) =>
                          value?.length > 128
                            ? Promise.reject(new Error('最大长度不可超过128个字符'))
                            : Promise.resolve()
                      }
                    ]}
                  >
                    <Input placeholder="请输入主机" allowClear limitLength={128} autoComplete="off" />
                  </Form.Item>

                  <Form.Item
                    name="port"
                    label="端口"
                    rules={[
                      {required: true, message: '请输入端口'},
                      {pattern: RULE.connectionPort, message: RULE.connectionPortText}
                    ]}
                  >
                    <Input placeholder="请输入端口" allowClear limitLength={32} autoComplete="off" />
                  </Form.Item>

                  {Object.values(ERelationalConnectionType).includes(selectedType as any) && (
                    <Form.Item
                      name="database"
                      label="数据库名称"
                      rules={[
                        {required: true, message: '请输入数据库名称'},
                        {
                          validator: (_, value) =>
                            value?.length > 128
                              ? Promise.reject(new Error('最大长度不可超过128个字符'))
                              : Promise.resolve()
                        }
                      ]}
                    >
                      <Input placeholder="请输入数据库名称" allowClear limitLength={128} autoComplete="off" />
                    </Form.Item>
                  )}

                  <Form.Item
                    name="user"
                    label="用户名"
                    rules={[
                      {required: true, message: '请输入用户名'},
                      {
                        validator: (_, value) =>
                          value?.length > 128
                            ? Promise.reject(new Error('最大长度不可超过128个字符'))
                            : Promise.resolve()
                      }
                    ]}
                  >
                    <Input placeholder="请输入用户名" allowClear limitLength={128} autoComplete="off" />
                  </Form.Item>

                  <Form.Item
                    name="passwd"
                    label="密码"
                    rules={[
                      {required: true, message: '请输入密码'},
                      {
                        validator: (_, value) =>
                          value?.length > 128
                            ? Promise.reject(new Error('最大长度不可超过128个字符'))
                            : Promise.resolve()
                      }
                    ]}
                  >
                    <Input.Password
                      placeholder="请输入密码"
                      visibilityToggle={false}
                      // @ts-ignore
                      allowClear
                      limitLength={128}
                      autoComplete="off"
                    />
                  </Form.Item>
                </>
              ) : (
                <>
                  <Form.Item
                    name="defaultFs"
                    label="defaultFS"
                    rules={[{required: true, message: '请输入defaultFS'}]}
                  >
                    <Input placeholder="请输入defaultFS" allowClear limitLength={256} autoComplete="off" />
                  </Form.Item>
                  <Form.Item name="hdfsSitePath" label="hdfs_site_path">
                    <FilePathSelectWorkarea
                      selectNodeType={FileNodeTypeEnum.FILE}
                      selectFileSuffix={['xml']}
                    />
                  </Form.Item>
                  <Form.Item
                    name="authenticationType"
                    label="认证方式"
                    initialValue={AuthenticationTypeEnum.None}
                  >
                    <Radio.Group>
                      {Object.values(AuthenticationTypeEnum).map((item) => (
                        <Radio value={item} key={item}>
                          {AuthenticationTypeMap[item]}
                        </Radio>
                      ))}
                    </Radio.Group>
                  </Form.Item>
                  <Form.Item name={['authenticationConf', 'keytabPath']} label="keytab文件">
                    <FilePathSelectWorkarea
                      selectNodeType={FileNodeTypeEnum.FILE}
                      selectFileSuffix={['keytab']}
                    />
                  </Form.Item>
                  <Form.Item name={['authenticationConf', 'confPath']} label="conf文件">
                    <FilePathSelectWorkarea
                      selectNodeType={FileNodeTypeEnum.FILE}
                      selectFileSuffix={['conf']}
                    />
                  </Form.Item>
                  <Form.Item name={['authenticationConf', 'principal']} label="principal">
                    <Input placeholder="请输入principal" allowClear limitLength={256} autoComplete="off" />
                  </Form.Item>
                </>
              )}
            </div>
          </Form>

          <div className={cx(styles['content-test-wrapper'], {[styles['step-hidden']]: currentSetp === 0})}>
            <ConnectionTest validateForm={form.validateFields} scrollToError={scrollToError} />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default EditModal;

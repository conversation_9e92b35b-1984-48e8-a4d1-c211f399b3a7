.connection-test {
  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 12px;

    button {
      width: 32px;
      height: 32px;
    }
  }

  &-table {
    td {
      line-height: 1;
    }

    .td-test-bar {
      display: flex;
      align-items: center;

      :global {
        .acud-tag-render-container .circle:after {
          left: 4px;
        }
      }

      button {
        margin-right: 12px;
        font-family: PingFang SC;
        width: 76px;
        height: 24px;
      }

      .untest {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ff9326;

        svg {
          fill: none;
          margin-right: 4px;
        }
      }
    }

    &-empty {
      color: #000;

      svg {
        fill: none;
      }

      :global {
        .acud-empty-image {
          height: 80px;
          margin-bottom: 16px;
        }
      }
    }
  }
}

/**
 * @file catalog - 工作空间tab
 * <AUTHOR>
 */

import {FC, useCallback, useContext, useEffect, useMemo, useRef, useState} from 'react';
import RefreshButton from '@components/RefreshButton';
import {Button, Pagination, Search, Table, toast, Modal, Loading, Empty} from 'acud';
import {ColumnsType} from 'acud/lib/table';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {useRequest} from 'ahooks';
import EditModal from './CreateModal';
import {
  ICatalogDetailRes,
  getCatalogWorkspaceList,
  ICatalogWorkspace,
  IQueryCatalogWorkspaceListParams,
  deleteCatalogWorkspace,
  CatalogType
} from '@api/metaRequest';
import styles from './index.module.less';
import {WorkspaceContext} from '@pages/index';

interface ICatalogWorkspaceProps {
  /** catalog id */
  catalog: ICatalogDetailRes;
  catalogName?: string;
  changeUrlFunReplace?: any; // 更改 URL 方法
}
const CatalogWorkspace: FC<ICatalogWorkspaceProps> = ({catalog, changeUrlFunReplace, catalogName}) => {
  const [workspaceList, setWorkspaceList] = useState<Array<ICatalogWorkspace>>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [keyword, setKeyword] = useState('');
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
    total: 0
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);

  // 工作空间id
  const {workspaceId} = useContext(WorkspaceContext);

  const {run: runWorkspaceList, loading: queryWorkspaceListLoading} = useRequest(getCatalogWorkspaceList, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        setWorkspaceList(res?.result?.items || []);
        setPagination((prev) => ({...prev, total: res?.result?.total || 0}));
        setSelectedRowKeys([]);
      }
    }
  });

  // 获取工作空间列表
  const getWorkspaceList = useCallback(
    (params: IQueryCatalogWorkspaceListParams = {}) => {
      if (params.pageNo) {
        setPagination((prev) => ({...prev, pageNo: params.pageNo!}));
      }
      runWorkspaceList(workspaceId, catalog?.id, {
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize,
        workspaceName: keyword,
        ...params
      });
    },
    [runWorkspaceList, pagination, keyword, catalog?.id, workspaceId]
  );

  // 初始化查询
  useEffect(() => {
    catalog?.id && getWorkspaceList();
  }, [catalog?.id]);

  // 数据总数
  const showTotal = useCallback(() => {
    return `共${pagination.total}条`;
  }, [pagination.total]);

  // 监听点击刷新按钮
  const onClickRefreshBtn = useCallback(() => {
    getWorkspaceList();
  }, [getWorkspaceList]);

  // 搜索
  const onConfirmSearch = useCallback(
    (value: string) => {
      setKeyword(value);
      getWorkspaceList({
        workspaceName: value,
        pageNo: 1
      });
    },
    [getWorkspaceList]
  );

  // 点击新建按钮
  const onClickAssignBtn = () => {
    setIsModalVisible(true);
  };

  // 关闭弹窗
  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  // 解绑
  const handleUnassion = useCallback(
    (workspace?: ICatalogWorkspace) => {
      const items = workspace ? [workspace.workspaceId] : selectedRowKeys;
      const msg = workspace ? `工作空间“${workspace.workspaceName}”` : '这些工作空间';
      Modal.confirm({
        title: '取消分配',
        content: `你确定要取消${msg}对“${catalog?.name}”的访问权限吗？`,
        onOk: () => {
          return deleteCatalogWorkspace(workspaceId, catalog?.id, {items}).then((res) => {
            if (res.success) {
              toast.success({
                message: '操作成功',
                duration: 5
              });
              getWorkspaceList();
              changeUrlFunReplace && changeUrlFunReplace((pre) => ({...pre}), true);
            }
          });
        }
      });
    },
    [catalog, selectedRowKeys]
  );

  const columns = useMemo(() => {
    const col: ColumnsType<ICatalogWorkspace> = [
      {
        title: '工作空间',
        dataIndex: 'workspaceName',
        width: '50%',
        key: 'workspaceName',
        ellipsis: {showTitle: false},
        render: (name) => {
          return <Ellipsis tooltip={name}>{name}</Ellipsis>;
        }
      },
      {
        title: '空间ID',
        dataIndex: 'workspaceId',
        width: '50%',
        key: 'workspaceId'
      },
      {
        title: '操作',
        width: '33%',
        render: (record: ICatalogWorkspace) => {
          return (
            <a
              onClick={() => {
                handleUnassion(record);
              }}
            >
              取消分配
            </a>
          );
        }
      }
    ];
    return col;
  }, [handleUnassion]);

  return (
    <div className={styles['catalog-workspace-tab-wrapper']}>
      <Loading loading={queryWorkspaceListLoading} />
      <div className={styles['space-tip']}>指定哪些⼯作空间可以访问此catalog</div>
      <div className={styles['operation-container']}>
        <div className={styles['left-btn-container']}>
          <Search
            placeholder="请输入名称进行搜索"
            className={styles['search-container']}
            allowClear
            onSearch={onConfirmSearch}
          />
          <Button onClick={() => handleUnassion()} disabled={selectedRowKeys.length === 0}>
            批量取消分配
          </Button>
          {/* <div>{showTotal()}</div> */}
        </div>
        <div className={styles['right-container']}>
          <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
          <Button
            type="primary"
            onClick={onClickAssignBtn}
            disabled={catalogName && catalogName === CatalogType.SYSTEM}
          >
            分配空间
          </Button>
        </div>
      </div>

      <Table
        dataSource={workspaceList}
        columns={columns}
        rowKey="workspaceId"
        scroll={pagination.total > 0 ? {y: 'calc(100vh - 400px)'} : undefined}
        rowSelection={{
          selectedRowKeys,
          onChange: (selectedRowKeys) => setSelectedRowKeys(selectedRowKeys)
        }}
        pagination={false}
        locale={{
          emptyText: (
            <Empty
              className="test"
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            />
          )
        }}
      />

      {pagination.total > 0 && (
        <div className={styles['pagination-container']}>
          <Pagination
            showSizeChanger={true}
            showQuickJumper={true}
            showTotal={showTotal}
            current={pagination.pageNo}
            total={pagination.total}
            onChange={(page, pageSize = 10) => {
              setPagination((prev) => ({
                ...prev,
                pageNo: page,
                pageSize: pageSize
              }));
              getWorkspaceList({
                pageNo: page,
                pageSize: pageSize
              });
            }}
          />
        </div>
      )}
      <EditModal
        catalogId={catalog?.id}
        isModalVisible={isModalVisible}
        handleCloseModal={handleCloseModal}
        getCatalogWorkspaceList={getWorkspaceList}
        changeUrlFunReplace={changeUrlFunReplace}
      />
    </div>
  );
};

export default CatalogWorkspace;
